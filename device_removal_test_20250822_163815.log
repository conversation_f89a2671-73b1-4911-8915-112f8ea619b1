--------- beginning of main
08-22 16:38:15.856   746  3209 D audio_hw_primary: start_output_stream: enter: stream(0xf2944330)usecase(1: low-latency-playback) devices(0x2) is_haptic_usecase(0)
08-22 16:38:15.857   746  3209 D audio_hw_primary: select_devices for use case (low-latency-playback)
08-22 16:38:15.857   746  3209 I msm8974_platform: platform_check_and_set_codec_backend_cfg:becf: afe: bitwidth 16, samplerate 48000 channels 2, backend_idx 0 usecase = 1 device (speaker)
08-22 16:38:15.857   746  3209 I msm8974_platform: platform_check_and_set_codec_backend_cfg: new_snd_devices[0] is 2
08-22 16:38:15.857   746  3209 I msm8974_platform: platform_check_codec_backend_cfg:becf: afe: bitwidth 16, samplerate 48000 channels 2, backend_idx 0 usecase = 1 device (speaker)
08-22 16:38:15.858 15954 15954 D activity: CurrentActivity -> com.bodymount.app.MainActivity
08-22 16:38:15.859   746  3209 D msm8974_platform: platform_check_codec_backend_cfg:becf: updated afe: bitwidth 16, samplerate 48000 channels 2,backend_idx 0 usecase = 1 device (speaker)
08-22 16:38:15.860   746  3209 I msm8974_platform: platform_check_codec_backend_cfg:becf: afe: Codec selected backend: 0 updated bit width: 16 and sample rate: 48000
08-22 16:38:15.860   746  3209 D audio_hw_primary: check_usecases_codec_backend:becf: force routing 0
08-22 16:38:15.860   746  3209 D audio_hw_primary: check_usecases_codec_backend:becf: (85) check_usecases curr device: speaker, usecase device: backends match 0
08-22 16:38:15.860   746  3209 D audio_hw_primary: check_usecases_codec_backend:becf: check_usecases num.of Usecases to switch 0
08-22 16:38:15.860   746  3209 D hardware_info: hw_info_append_hw_type : device_name = speaker
08-22 16:38:15.860   746  3209 D audio_hw_primary: enable_snd_device: snd_device(2: speaker)
08-22 16:38:15.860   746  3209 D msm8974_platform: platform_get_island_cfg_on_device:island cfg status on snd_device = (speaker 0)
08-22 16:38:15.860   746  3209 I soundtrigger: audio_extn_sound_trigger_update_device_status: device 0x2 of type 0 for Event 1, with Raise=0
08-22 16:38:15.860   746  3209 D audio_route: Apply path: speaker
--------- beginning of system
08-22 16:38:15.861  1359  1806 I ActivityTaskManager: START u0 {cmp=com.bodymount.app/.MainActivity} from uid 10223
08-22 16:38:15.865  1359  1806 D ActivityTrigger: ActivityTrigger activityPauseTrigger 
08-22 16:38:15.869   746  3209 D audio_hw_primary: audio_is_true_native_stream_active:napb: (0) (low-latency-playback)id (1) sr 48000 bw (16) device speaker
08-22 16:38:15.869   746  3209 D soundtrigger: audio_extn_sound_trigger_update_stream_status: uc_info->id 1 of type 0 for Event 3, with Raise=0
08-22 16:38:15.869   746  3209 D audio_hw_utils: audio_extn_utils_send_app_type_cfg: usecase->out_snd_device speaker
08-22 16:38:15.870   746  3209 I audio_hw_utils: send_app_type_cfg_for_device PLAYBACK app_type 69937, acdb_dev_id 15, sample_rate 48000, snd_device_be_idx 170
08-22 16:38:15.871   746  3209 D ACDB-LOADER: ACDB -> send_audio_cal, acdb_id = 15, path = 0, app id = 0x11131, sample rate = 48000, use_case = 0,buffer_idx_w_path =0, afe_sample_rate = 48000, cal_mode = 1, offset_index = 0
08-22 16:38:15.871   746  3209 D ACDB-LOADER: ACDB -> send_asm_topology
08-22 16:38:15.871   746  3209 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_STREAM_TOPOLOGY_ID
08-22 16:38:15.871   746  3209 D ACDB-LOADER: ACDB -> send_adm_topology
08-22 16:38:15.871   746  3209 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_COMMON_TOPOLOGY_ID
08-22 16:38:15.871   746  3209 D ACDB-LOADER: ACDB -> send_audtable
08-22 16:38:15.871   746  3209 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_COMMON_TABLE_SIZE
08-22 16:38:15.871   746  3209 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_COMMON_TABLE
08-22 16:38:15.871   746  3209 D ACDB-LOADER: ACDB -> AUDIO_SET_AUDPROC_CAL cal_type[11] acdb_id[15] app_type[69937]
08-22 16:38:15.871   746  3209 D ACDB-LOADER: ACDB -> send_audvoltable
08-22 16:38:15.871   746  3209 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_GAIN_DEP_STEP_TABLE_SIZE
08-22 16:38:15.871   746  3209 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_GAIN_DEP_STEP_TABLE, vol index 0
08-22 16:38:15.871   746  3209 D ACDB-LOADER: ACDB -> AUDIO_SET_VOL_CAL cal type = 12
08-22 16:38:15.871   746  3209 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_STREAM_TABLE_SIZE
08-22 16:38:15.871   746  3209 D ACDB-LOADER: ACDB -> send_audstrmtable
08-22 16:38:15.871   746  3209 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_STREAM_TABLE_V2
08-22 16:38:15.871   746  3209 D ACDB-LOADER: ACDB -> audstrm_cal->cal_type.cal_data.cal_size = 16
08-22 16:38:15.871   746  3209 D ACDB-LOADER: ACDB -> send_afe_topology
08-22 16:38:15.871   746  3209 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AFE_TOPOLOGY_ID
08-22 16:38:15.871   746  3209 D ACDB-LOADER: ACDB -> GET_AFE_TOPOLOGY_ID for adcd_id 15, Topology Id 1025e
08-22 16:38:15.871   746  3209 D ACDB-LOADER: ACDB -> send_afe_cal
08-22 16:38:15.871   746  3209 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AFE_COMMON_TABLE_SIZE
08-22 16:38:15.871   746  3209 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AFE_COMMON_TABLE
08-22 16:38:15.872   746  3209 D ACDB-LOADER: ACDB -> AUDIO_SET_AFE_CAL cal_type[16] acdb_id[15]
08-22 16:38:15.872   746  3209 D ACDB-LOADER: ACDB -> send_hw_delay : acdb_id = 15 path = 0
08-22 16:38:15.872   746  3209 D ACDB-LOADER: ACDB -> ACDB_AVSYNC_INFO: ACDB_CMD_GET_DEVICE_PROPERTY
08-22 16:38:15.872   746  3209 D audio_hw_primary: enable_audio_route: apply mixer and update path: low-latency-playback
08-22 16:38:15.872   746  3209 D audio_route: Apply path: low-latency-playback
08-22 16:38:15.873   746  3209 D audio_hw_primary: select_devices: done
08-22 16:38:15.873   746  3209 D msm8974_platform: platform_set_channel_map mixer_ctl_name:Playback Channel Map13
08-22 16:38:15.873   746  3209 D msm8974_platform: platform_set_channel_map: set mapping(1 2 0 0 0 0 0 0) for channel:2
08-22 16:38:15.875   746  3209 E msm8974_platform: platform_set_channel_map: Could not set ctl, error:-1 ch_count:2
08-22 16:38:15.885  1359  1806 I ActivityTaskManager: The Process com.bodymount.app Already Exists in BG. So sending its PID: 15954
08-22 16:38:15.885  1359  1806 W ActivityTaskManager: Tried to set launchTime (0) < mLastActivityLaunchTime (22966849)
08-22 16:38:15.886  1359  1806 D CompatibilityInfo: mCompatibilityFlags - 4
08-22 16:38:15.886  1359  1806 D CompatibilityInfo: applicationDensity - 356
08-22 16:38:15.886  1359  1806 D CompatibilityInfo: applicationScale - 1.0
08-22 16:38:15.894 15954 16005 I OpenGLRenderer: Davey! duration=9223349065391ms; Flags=0, FrameTimelineVsyncId=576356, IntendedVsync=22971463444857, Vsync=22971463444857, InputEventId=0, HandleInputStart=22971478692016, AnimationStart=22971478696808, PerformTraversalsStart=22971479153995, DrawStart=22971486764464, FrameDeadline=22971480111523, FrameInterval=22971478669620, FrameStartTime=16666666, SyncQueued=22971487319881, SyncStart=22971487378631, IssueDrawCommandsStart=22971487526079, SwapBuffers=22971489907224, FrameCompleted=9223372036854775807, DequeueBufferDuration=21615, QueueBufferDuration=489895, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22971491466599, DisplayPresentTime=0, 
08-22 16:38:15.949   746  3209 D audio_hw_primary: start_output_stream: exit
08-22 16:38:16.237 15954 15954 D CompatibilityChangeReporter: Compat change id reported: 171228096; UID 10223; state: ENABLED
08-22 16:38:16.870  1359  1969 I ActivityTaskManager: START u0 {act=android.content.pm.action.REQUEST_PERMISSIONS pkg=com.android.permissioncontroller cmp=com.android.permissioncontroller/.permission.ui.GrantPermissionsActivity (has extras)} from uid 10223
08-22 16:38:16.878  1359  1969 D ActivityTrigger: ActivityTrigger activityPauseTrigger 
08-22 16:38:16.937 15954 15954 D BottomBarPostUi: TODO
08-22 16:38:16.959 15954 15954 W Activity: Can request only one set of permissions at a time
08-22 16:38:17.220 15954 15954 D managerObj: postUiInitActions:com.bodymount.app.sensor.BluetoothManager@2a2ff8d 
08-22 16:38:17.234  1359  1969 W WindowManager: Failed looking up window session=Session{d277c8 15954:u0a10223} callers=com.android.server.wm.WindowManagerService.windowForClientLocked:5775 com.android.server.wm.Session.updateRequestedVisibilities:640 android.view.IWindowSession$Stub.onTransact:1175 
08-22 16:38:17.247 15954 15993 D valid   : parameter Ecg
08-22 16:38:17.249 15954 15993 D valid   : parameter BpSys
08-22 16:38:17.251 15954 15993 I AlarmManSib: Play alarm->**********
08-22 16:38:17.271  1359 13928 D ConnectivityService: requestNetwork for uid/pid:10223/15954 activeRequest: null callbackRequest: 67 [NetworkRequest [ REQUEST id=68, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED Uid: 10223 RequestorUid: 10223 RequestorPkg: com.bodymount.app] ]] callback flags: 0 priority: **********
08-22 16:38:17.273  1359  1498 D ConnectivityService: NetReassign [68 : null → 100]
08-22 16:38:17.273  2505  2537 W MediaProvider: Failed to fetch original file descriptor
08-22 16:38:17.273  2505  2537 W MediaProvider: java.io.FileNotFoundException: Input file descriptor is already original
08-22 16:38:17.273  2505  2537 W MediaProvider: 	at com.android.providers.media.MediaProvider.getOriginalMediaFormatFileDescriptor(MediaProvider.java:5746)
08-22 16:38:17.273  2505  2537 W MediaProvider: 	at com.android.providers.media.MediaProvider.openTypedAssetFileCommon(MediaProvider.java:7162)
08-22 16:38:17.273  2505  2537 W MediaProvider: 	at com.android.providers.media.MediaProvider.openTypedAssetFile(MediaProvider.java:7143)
08-22 16:38:17.273  2505  2537 W MediaProvider: 	at android.content.ContentProvider$Transport.openTypedAssetFile(ContentProvider.java:548)
08-22 16:38:17.273  2505  2537 W MediaProvider: 	at android.content.ContentProviderNative.onTransact(ContentProviderNative.java:327)
08-22 16:38:17.273  2505  2537 W MediaProvider: 	at android.os.Binder.execTransactInternal(Binder.java:1179)
08-22 16:38:17.273  2505  2537 W MediaProvider: 	at android.os.Binder.execTransact(Binder.java:1143)
08-22 16:38:17.277 15954 15954 D CompatibilityChangeReporter: Compat change id reported: 160794467; UID 10223; state: ENABLED
08-22 16:38:17.279  1359  1491 D WifiNetworkFactory: got request NetworkRequest [ REQUEST id=68, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED Uid: 10223 RequestorUid: 10223 RequestorPkg: com.bodymount.app] ]
08-22 16:38:17.280  1359  1491 D UntrustedWifiNetworkFactory: got request NetworkRequest [ REQUEST id=68, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED Uid: 10223 RequestorUid: 10223 RequestorPkg: com.bodymount.app] ]
08-22 16:38:17.281  1359  1491 D OemPaidWifiNetworkFactory: got request NetworkRequest [ REQUEST id=68, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED Uid: 10223 RequestorUid: 10223 RequestorPkg: com.bodymount.app] ]
08-22 16:38:17.288  1212  2019 V AudioSink: AudioOutput(11793)
08-22 16:38:17.289  1212  2019 I ExtendedNuUtils: printFileName fd(10) -> /data/app/~~8sob2rQ304dc_blJZg7EJw==/com.bodymount.app-_dwS8VFRWAOmL9k5QVHcxw==/base.apk
08-22 16:38:17.290  1212  2019 E AudioSystem: invalid attributes { Content type: AUDIO_CONTENT_TYPE_UNKNOWN Usage: AUDIO_USAGE_UNKNOWN Source: AUDIO_SOURCE_INVALID Flags: 0x800 Tags:  } when converting to stream
08-22 16:38:17.302  1101  1187 I MMParserExtractor: Created(0xb400007514847550)
08-22 16:38:17.302  1101  1187 E MM_OSAL : FileSource::FileSource
08-22 16:38:17.302  1101  1187 E MM_OSAL : FileSource::FileSource m_bEveryThingOK 1
08-22 16:38:17.303 15954 15954 I Choreographer: Skipped 84 frames!  The application may be doing too much work on its main thread.
08-22 16:38:17.307  1101  1187 D MediaBufferGroup: creating MemoryDealer
08-22 16:38:17.317 15954 15993 D valid   : parameter Fall
08-22 16:38:17.317 15954 15993 D valid   : parameter BpDia
08-22 16:38:17.317 15954 15993 D valid   : parameter Spo2
08-22 16:38:17.317 15954 15993 I AlarmManSib: Play alarm->**********
08-22 16:38:17.378  1359  1390 W ActivityTaskManager: Activity top resumed state loss timeout for ActivityRecord{37bb115 u0 com.bodymount.app/.MainActivity t41}
08-22 16:38:17.378  1359  1390 W ActivityTaskManager: Activity pause timeout for ActivityRecord{37bb115 u0 com.bodymount.app/.MainActivity t41}
08-22 16:38:17.381  1359  1390 I ActivityTaskManager: The Process com.android.permissioncontroller Already Exists in BG. So sending its PID: 15869
08-22 16:38:17.382  1359  1390 W ActivityTaskManager: Tried to set launchTime (0) < mLastActivityLaunchTime (22968644)
08-22 16:38:17.384  1359  1390 D CompatibilityInfo: mCompatibilityFlags - 4
08-22 16:38:17.384  1359  1390 D CompatibilityInfo: applicationDensity - 356
08-22 16:38:17.384  1359  1390 D CompatibilityInfo: applicationScale - 1.0
08-22 16:38:17.505  1359  1806 D ActivityTrigger: ActivityTrigger activityPauseTrigger 
08-22 16:38:17.577 15954 15954 W RecyclerView: No adapter attached; skipping layout
08-22 16:38:17.678 15954 15970 I OpenGLRenderer: Davey! duration=1780ms; Flags=1, FrameTimelineVsyncId=576362, IntendedVsync=22971496725489, Vsync=22972896725433, InputEventId=0, HandleInputStart=22972909244568, AnimationStart=22972909257120, PerformTraversalsStart=22972911333734, DrawStart=22973185991234, FrameDeadline=22971513392155, FrameInterval=22972908952953, FrameStartTime=16666666, SyncQueued=22973203779255, SyncStart=22973204377224, IssueDrawCommandsStart=22973204508474, SwapBuffers=22973269005088, FrameCompleted=22973277924411, DequeueBufferDuration=600208, QueueBufferDuration=1433333, GpuCompleted=22973277924411, SwapBuffersCompleted=22973270972484, DisplayPresentTime=0, 
08-22 16:38:18.029 15954 16005 I OpenGLRenderer: Davey! duration=9223349065357ms; Flags=1, FrameTimelineVsyncId=576362, IntendedVsync=22971496725489, Vsync=22972896725433, InputEventId=0, HandleInputStart=22972909244568, AnimationStart=22972909257120, PerformTraversalsStart=22972911333734, DrawStart=22973490795557, FrameDeadline=22971513392155, FrameInterval=22972908952953, FrameStartTime=16666666, SyncQueued=22973563696651, SyncStart=22973564067744, IssueDrawCommandsStart=22973564762484, SwapBuffers=22973623210661, FrameCompleted=9223372036854775807, DequeueBufferDuration=1325781, QueueBufferDuration=724479, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22973624295869, DisplayPresentTime=0, 
08-22 16:38:18.029 15954 16005 I OpenGLRenderer: Davey! duration=9223349065357ms; Flags=1, FrameTimelineVsyncId=576362, IntendedVsync=22971496725489, Vsync=22972896725433, InputEventId=0, HandleInputStart=22972909244568, AnimationStart=22972909257120, PerformTraversalsStart=22972911333734, DrawStart=22973625877536, FrameDeadline=22971530058821, FrameInterval=22972908952953, FrameStartTime=16666666, SyncQueued=22973626136286, SyncStart=22973626310036, IssueDrawCommandsStart=22973626652536, SwapBuffers=22973627114203, FrameCompleted=9223372036854775807, DequeueBufferDuration=52656, QueueBufferDuration=781615, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22973628397484, DisplayPresentTime=0, 
08-22 16:38:18.203 15954 15954 D DatabaseDebug: Passphrase: mykey
08-22 16:38:18.330 15954 15954 W Glide   : Failed to find GeneratedAppGlideModule. You should include an annotationProcessor compile dependency on com.github.bumptech.glide:compiler in your application and a @GlideModule annotated AppGlideModule implementation or LibraryGlideModules will be silently ignored
08-22 16:38:18.350  1359  1806 W InputManager-JNI: Input channel object '86fd6df com.android.permissioncontroller/com.android.permissioncontroller.permission.ui.GrantPermissionsActivity (client)' was disposed without first being removed with the input manager!
08-22 16:38:18.449  1593  2663 D BluetoothAdapterService: startDiscovery
08-22 16:38:18.452  1593  2395 W bt_btif : bta_dm_check_av:0
08-22 16:38:18.452   749   749 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK ON
08-22 16:38:18.455   749   749 D vendor.qti.bluetooth@1.0-wake_lock: Acquire wakelock is acquired 
08-22 16:38:18.455   749   749 I vendor.qti.bluetooth@1.0-ibs_handler: DeviceWakeUp: Writing IBS_WAKE_IND
08-22 16:38:18.458   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_ACK: 0xFC
08-22 16:38:18.458   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Signal wack_cond_
08-22 16:38:18.458   749   749 D vendor.qti.bluetooth@1.0-ibs_handler: DeviceWakeUp: Unblocked from waiting for FC, pthread_cond_timedwait ret = 0
08-22 16:38:18.459   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_IND: 0xFD
08-22 16:38:18.460   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Writing IBS_WAKE_ACK
08-22 16:38:18.461  1593  2395 E bt_btif : bte_scan_filt_param_cfg_evt, 23
08-22 16:38:18.471 15954 15954 I Choreographer: Skipped 69 frames!  The application may be doing too much work on its main thread.
08-22 16:38:18.484  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:18.484  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:18.484  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:18.485  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:18.485  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:18.485  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr f0:9e:9e:0f:e9:25
08-22 16:38:18.485  1593  1743 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr f0:9e:9e:0f:e9:25
08-22 16:38:18.485  1593  1743 D bt_btif_config: btif_get_device_type: Device [f0:9e:9e:0f:e9:25] type 3
08-22 16:38:18.485  1593  1743 D BluetoothRemoteDevices: Property type: 2
08-22 16:38:18.486  1593  1743 D BluetoothRemoteDevices: Remote Address is:F0:9E:9E:0F:E9:25
08-22 16:38:18.486  1593  1743 D BluetoothRemoteDevices: Property type: 1
08-22 16:38:18.486  1593  1743 D BluetoothRemoteDevices: Skip name update for F0:9E:9E:0F:E9:25
08-22 16:38:18.486  1593  1743 D BluetoothRemoteDevices: Property type: 5
08-22 16:38:18.486  1593  1743 D BluetoothRemoteDevices: BT_PROPERTY_TYPE_OF_DEVICE F0:9E:9E:0F:E9:25
08-22 16:38:18.486  1593  1743 D BluetoothRemoteDevices: Property type: 11
08-22 16:38:18.486  1593  1743 D BluetoothRemoteDevices: deviceFoundCallback: Remote Address is:F0:9E:9E:0F:E9:25
08-22 16:38:18.494  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=0e:7c:ce:be:dc:e7
08-22 16:38:18.494  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=0e:7c:ce:be:dc:e7
08-22 16:38:18.494  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:18.509  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=e2:50:9f:15:8f:9c
08-22 16:38:18.509  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=e2:50:9f:15:8f:9c
08-22 16:38:18.509  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response e2:50:9f:15:8f:9c
08-22 16:38:18.512  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=e2:50:9f:15:8f:9c
08-22 16:38:18.512  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=e2:50:9f:15:8f:9c
08-22 16:38:18.512  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:18.513 15954 15971 I OpenGLRenderer: Davey! duration=9223349063941ms; Flags=0, FrameTimelineVsyncId=576495, IntendedVsync=22972913300645, Vsync=22974063300599, InputEventId=0, HandleInputStart=22974077729776, AnimationStart=22974077738421, PerformTraversalsStart=22974079446442, DrawStart=22974093241390, FrameDeadline=22972946633977, FrameInterval=22974077527744, FrameStartTime=16666666, SyncQueued=22974103243265, SyncStart=22974103538057, IssueDrawCommandsStart=22974103769619, SwapBuffers=22974108916494, FrameCompleted=9223372036854775807, DequeueBufferDuration=812812, QueueBufferDuration=1052813, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22974111405661, DisplayPresentTime=0, 
08-22 16:38:18.529 15954 15971 I OpenGLRenderer: Davey! duration=1213ms; Flags=0, FrameTimelineVsyncId=576495, IntendedVsync=22972913300645, Vsync=22974063300599, InputEventId=0, HandleInputStart=22974077729776, AnimationStart=22974077738421, PerformTraversalsStart=22974079446442, DrawStart=22974112212692, FrameDeadline=22972946633977, FrameInterval=22974077527744, FrameStartTime=16666666, SyncQueued=22974114301338, SyncStart=22974114536911, IssueDrawCommandsStart=22974114674619, SwapBuffers=22974121318421, FrameCompleted=22974127386911, DequeueBufferDuration=4652084, QueueBufferDuration=1095469, GpuCompleted=22974127386911, SwapBuffersCompleted=22974124215713, DisplayPresentTime=0, 
08-22 16:38:18.531  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=22:21:1c:16:71:10
08-22 16:38:18.531  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=22:21:1c:16:71:10
08-22 16:38:18.531  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 22:21:1c:16:71:10
08-22 16:38:18.532  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:18.532  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:18.532  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:18.532  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:18.532  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:18.532  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:18.542  1359  1806 W ActivityManager: Receiver with filter android.content.IntentFilter@5a8e953 already registered for pid 15954, callerPackage is com.bodymount.app
08-22 16:38:18.564  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=fc:a8:9b:1e:87:57
08-22 16:38:18.564  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=fc:a8:9b:1e:87:57
08-22 16:38:18.564  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response fc:a8:9b:1e:87:57
08-22 16:38:18.565  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=22:68:f4:28:0f:21
08-22 16:38:18.565  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=22:68:f4:28:0f:21
08-22 16:38:18.565  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:18.567  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=25:13:9d:26:84:51
08-22 16:38:18.567  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=25:13:9d:26:84:51
08-22 16:38:18.567  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:18.580  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=e4:fa:5b:59:33:6b
08-22 16:38:18.580  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=e4:fa:5b:59:33:6b
08-22 16:38:18.580  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response e4:fa:5b:59:33:6b
08-22 16:38:18.582  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:18.582  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:18.582  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:18.582  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:18.582  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:18.582  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:18.586  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=ff:8b:e6:0b:72:a3
08-22 16:38:18.586  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=ff:8b:e6:0b:72:a3
08-22 16:38:18.586  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response ff:8b:e6:0b:72:a3
08-22 16:38:18.586  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=ff:8b:e6:0b:72:a3
08-22 16:38:18.586  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=ff:8b:e6:0b:72:a3
08-22 16:38:18.586  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:18.588  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=94:32:51:02:2c:19
08-22 16:38:18.588  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=94:32:51:02:2c:19
08-22 16:38:18.588  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 94:32:51:02:2c:19
08-22 16:38:18.588  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=94:32:51:02:2c:19
08-22 16:38:18.588  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=94:32:51:02:2c:19
08-22 16:38:18.588  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:18.596  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=3a:3e:4c:db:1c:5e
08-22 16:38:18.597  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=3a:3e:4c:db:1c:5e
08-22 16:38:18.597  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:18.599  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=0e:7c:ce:be:dc:e7
08-22 16:38:18.599  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=0e:7c:ce:be:dc:e7
08-22 16:38:18.599  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 0e:7c:ce:be:dc:e7
08-22 16:38:18.603  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=52:b7:d9:95:37:dd
08-22 16:38:18.603  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=52:b7:d9:95:37:dd
08-22 16:38:18.604  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 52:b7:d9:95:37:dd
08-22 16:38:18.604  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=52:b7:d9:95:37:dd
08-22 16:38:18.604  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=52:b7:d9:95:37:dd
08-22 16:38:18.604  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:18.611 15954 16061 D ProfileInstaller: Installing profile for com.bodymount.app
08-22 16:38:18.615  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=44:54:82:95:10:54
08-22 16:38:18.615  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=44:54:82:95:10:54
08-22 16:38:18.615  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 44:54:82:95:10:54
08-22 16:38:18.615  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=6b:28:4f:3d:e7:16
08-22 16:38:18.615  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=6b:28:4f:3d:e7:16
08-22 16:38:18.616  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 6b:28:4f:3d:e7:16
08-22 16:38:18.616  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=6b:28:4f:3d:e7:16
08-22 16:38:18.616  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=6b:28:4f:3d:e7:16
08-22 16:38:18.616  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:18.622  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=51:1e:c0:95:11:11
08-22 16:38:18.622  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=51:1e:c0:95:11:11
08-22 16:38:18.622  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 51:1e:c0:95:11:11
08-22 16:38:18.641  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=c1:22:33:45:0b:4a
08-22 16:38:18.641  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=c1:22:33:45:0b:4a
08-22 16:38:18.641  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response c1:22:33:45:0b:4a
08-22 16:38:18.641  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=e5:53:7c:25:b6:37
08-22 16:38:18.641  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=e5:53:7c:25:b6:37
08-22 16:38:18.641  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response e5:53:7c:25:b6:37
08-22 16:38:18.642  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=e5:53:7c:25:b6:37
08-22 16:38:18.642  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=e5:53:7c:25:b6:37
08-22 16:38:18.642  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:18.660 15954 15954 D BluetoothDiscovery: Discovered target device: BMPMS 206, MAC: F0:9E:9E:0F:E9:25, RSSI: -45 dBm
08-22 16:38:18.662  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=fc:a8:9b:1e:87:57
08-22 16:38:18.662  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=fc:a8:9b:1e:87:57
08-22 16:38:18.662  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response fc:a8:9b:1e:87:57
08-22 16:38:18.663  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:18.663  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:18.663  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:18.663  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:18.663  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:18.663  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:18.667  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=25:13:9d:26:84:51
08-22 16:38:18.667  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=25:13:9d:26:84:51
08-22 16:38:18.667  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 25:13:9d:26:84:51
08-22 16:38:18.674  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=23:da:da:c8:2d:cf
08-22 16:38:18.674  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=23:da:da:c8:2d:cf
08-22 16:38:18.674  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:18.690  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=e4:fa:5b:59:33:6b
08-22 16:38:18.690  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=e4:fa:5b:59:33:6b
08-22 16:38:18.690  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response e4:fa:5b:59:33:6b
08-22 16:38:18.690  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=06:ad:20:9e:b9:fd
08-22 16:38:18.690  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=06:ad:20:9e:b9:fd
08-22 16:38:18.690  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:18.696 15954 15971 I OpenGLRenderer: Davey! duration=9223349062758ms; Flags=0, FrameTimelineVsyncId=576625, IntendedVsync=22974096407671, Vsync=22974263074331, InputEventId=0, HandleInputStart=22974269989775, AnimationStart=22974269998734, PerformTraversalsStart=22974271962640, DrawStart=22974280542119, FrameDeadline=22974129741003, FrameInterval=22974269952536, FrameStartTime=16666666, SyncQueued=22974286491546, SyncStart=22974286804411, IssueDrawCommandsStart=22974287064046, SwapBuffers=22974293179255, FrameCompleted=9223372036854775807, DequeueBufferDuration=963907, QueueBufferDuration=1197136, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22974294747692, DisplayPresentTime=0, 
08-22 16:38:18.700  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=0e:7c:ce:be:dc:e7
08-22 16:38:18.700  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=0e:7c:ce:be:dc:e7
08-22 16:38:18.700  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 0e:7c:ce:be:dc:e7
08-22 16:38:18.725  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=5e:b4:71:02:e3:55
08-22 16:38:18.725  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=5e:b4:71:02:e3:55
08-22 16:38:18.725  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 5e:b4:71:02:e3:55
08-22 16:38:18.726  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=5d:e1:1e:ff:f5:ab
08-22 16:38:18.726  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=5d:e1:1e:ff:f5:ab
08-22 16:38:18.726  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 5d:e1:1e:ff:f5:ab
08-22 16:38:18.726  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=5d:e1:1e:ff:f5:ab
08-22 16:38:18.726  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=5d:e1:1e:ff:f5:ab
08-22 16:38:18.726  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:18.736  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=5e:ac:ee:80:be:93
08-22 16:38:18.736  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=5e:ac:ee:80:be:93
08-22 16:38:18.736  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 5e:ac:ee:80:be:93
08-22 16:38:18.736  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=5e:ac:ee:80:be:93
08-22 16:38:18.736  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=5e:ac:ee:80:be:93
08-22 16:38:18.736  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 5e:ac:ee:80:be:93
08-22 16:38:18.737  1593  1743 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 5e:ac:ee:80:be:93
08-22 16:38:18.738  1593  1743 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 5e:ac:ee:80:be:93
08-22 16:38:18.738  1593  1743 D BluetoothRemoteDevices: Added new device property
08-22 16:38:18.739  1593  1743 D BluetoothRemoteDevices: Removing device 76:CA:46:00:00:68 from property map
08-22 16:38:18.739  1593  1743 D BluetoothRemoteDevices: Property type: 2
08-22 16:38:18.739  1593  1743 D BluetoothRemoteDevices: Remote Address is:5E:AC:EE:80:BE:93
08-22 16:38:18.739  1593  1743 D BluetoothRemoteDevices: Property type: 5
08-22 16:38:18.739  1593  1743 D BluetoothRemoteDevices: BT_PROPERTY_TYPE_OF_DEVICE 5E:AC:EE:80:BE:93
08-22 16:38:18.739  1593  1743 D BluetoothRemoteDevices: Property type: 11
08-22 16:38:18.739  1593  1743 D BluetoothRemoteDevices: deviceFoundCallback: Remote Address is:5E:AC:EE:80:BE:93
08-22 16:38:18.744  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f2:03:30:3e:10:74
08-22 16:38:18.744  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f2:03:30:3e:10:74
08-22 16:38:18.745  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f2:03:30:3e:10:74
08-22 16:38:18.745  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f2:03:30:3e:10:74
08-22 16:38:18.745  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f2:03:30:3e:10:74
08-22 16:38:18.745  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:18.784   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_SLEEP_IND: 0xFE
08-22 16:38:18.796 15954 16005 I OpenGLRenderer: Davey! duration=9223349062758ms; Flags=0, FrameTimelineVsyncId=576625, IntendedVsync=22974096407671, Vsync=22974263074331, InputEventId=0, HandleInputStart=22974269989775, AnimationStart=22974269998734, PerformTraversalsStart=22974271962640, DrawStart=22974387739098, FrameDeadline=22974129741003, FrameInterval=22974269952536, FrameStartTime=16666666, SyncQueued=22974389623734, SyncStart=22974389926390, IssueDrawCommandsStart=22974390187900, SwapBuffers=22974394947328, FrameCompleted=9223372036854775807, DequeueBufferDuration=1231511, QueueBufferDuration=893125, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22974396318421, DisplayPresentTime=0, 
08-22 16:38:18.835 15954 15954 D BluetoothDiscovery: Discovered unexpected device: Unknown Device, MAC: 5E:AC:EE:80:BE:93, RSSI: -66 dBm
08-22 16:38:18.861 15954 16005 I OpenGLRenderer: Davey! duration=9223349062424ms; Flags=0, FrameTimelineVsyncId=576647, IntendedVsync=22974429882474, Vsync=22974446549140, InputEventId=0, HandleInputStart=22974453525869, AnimationStart=22974453530713, PerformTraversalsStart=22974454348265, DrawStart=22974454826130, FrameDeadline=22974463215806, FrameInterval=22974453508942, FrameStartTime=16666666, SyncQueued=22974455507692, SyncStart=22974455786859, IssueDrawCommandsStart=22974455928421, SwapBuffers=22974458212848, FrameCompleted=9223372036854775807, DequeueBufferDuration=22813, QueueBufferDuration=741875, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22974459471182, DisplayPresentTime=0, 
08-22 16:38:18.871  1359  2447 E TaskPersister: File error accessing recents directory (directory doesn't exist?).
08-22 16:38:19.099   746   746 D audio_hw_primary: out_standby: enter: stream (0xf2944330) usecase(1: low-latency-playback)
08-22 16:38:19.162   746   746 D audio_hw_primary: disable_audio_route: reset and update mixer path: low-latency-playback
08-22 16:38:19.166   746   746 D soundtrigger: audio_extn_sound_trigger_update_stream_status: uc_info->id 1 of type 0 for Event 2, with Raise=0
08-22 16:38:19.166   746   746 D hardware_info: hw_info_append_hw_type : device_name = speaker
08-22 16:38:19.166   746   746 D audio_hw_primary: disable_snd_device: snd_device(2: speaker)
08-22 16:38:19.178   746   746 I soundtrigger: audio_extn_sound_trigger_update_device_status: device 0x2 of type 0 for Event 0, with Raise=0
08-22 16:38:19.280   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_IND: 0xFD
08-22 16:38:19.280   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Writing IBS_WAKE_ACK
08-22 16:38:19.283  1593  2395 W bt_btm  : btm_process_inq_results: BDA: 30:e7:bc:de:fa:ae
08-22 16:38:19.283  1593  2395 W bt_btm  : btm_process_inq_results: Dev class: 5a-02-0c
08-22 16:38:19.283  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 30:e7:bc:de:fa:ae
08-22 16:38:19.285  1593  1743 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 30:e7:bc:de:fa:ae
08-22 16:38:19.286  1593  1743 D BluetoothRemoteDevices: Added new device property
08-22 16:38:19.287  1593  1743 D BluetoothRemoteDevices: Removing device 3D:93:B7:23:F5:93 from property map
08-22 16:38:19.287  1593  1743 D BluetoothRemoteDevices: Property type: 2
08-22 16:38:19.287  1593  1743 D BluetoothRemoteDevices: Remote Address is:30:E7:BC:DE:FA:AE
08-22 16:38:19.287  1593  1743 D BluetoothRemoteDevices: Property type: 4
08-22 16:38:19.287  1593  1743 D BluetoothRemoteDevices: Remote after adv audio class is:7936 30:E7:BC:DE:FA:AE
08-22 16:38:19.290  1593  1743 D BluetoothRemoteDevices: Remote class is:5898764
08-22 16:38:19.290  1593  1743 D BluetoothRemoteDevices: Property type: 5
08-22 16:38:19.290  1593  1743 D BluetoothRemoteDevices: BT_PROPERTY_TYPE_OF_DEVICE 30:E7:BC:DE:FA:AE
08-22 16:38:19.290  1593  1743 D BluetoothRemoteDevices: Property type: 11
08-22 16:38:19.291  1593  1743 D BluetoothRemoteDevices: deviceFoundCallback: Remote Address is:30:E7:BC:DE:FA:AE
08-22 16:38:19.304 15954 15954 D BluetoothDiscovery: Discovered unexpected device: Unknown Device, MAC: 30:E7:BC:DE:FA:AE, RSSI: -81 dBm
08-22 16:38:19.322   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_SLEEP_IND: 0xFE
08-22 16:38:19.326  2505  2537 W MediaProvider: Failed to fetch original file descriptor
08-22 16:38:19.326  2505  2537 W MediaProvider: java.io.FileNotFoundException: Input file descriptor is already original
08-22 16:38:19.326  2505  2537 W MediaProvider: 	at com.android.providers.media.MediaProvider.getOriginalMediaFormatFileDescriptor(MediaProvider.java:5746)
08-22 16:38:19.326  2505  2537 W MediaProvider: 	at com.android.providers.media.MediaProvider.openTypedAssetFileCommon(MediaProvider.java:7162)
08-22 16:38:19.326  2505  2537 W MediaProvider: 	at com.android.providers.media.MediaProvider.openTypedAssetFile(MediaProvider.java:7143)
08-22 16:38:19.326  2505  2537 W MediaProvider: 	at android.content.ContentProvider$Transport.openTypedAssetFile(ContentProvider.java:548)
08-22 16:38:19.326  2505  2537 W MediaProvider: 	at android.content.ContentProviderNative.onTransact(ContentProviderNative.java:327)
08-22 16:38:19.326  2505  2537 W MediaProvider: 	at android.os.Binder.execTransactInternal(Binder.java:1179)
08-22 16:38:19.326  2505  2537 W MediaProvider: 	at android.os.Binder.execTransact(Binder.java:1143)
08-22 16:38:19.331  1212  2019 V AudioSink: AudioOutput(11809)
08-22 16:38:19.332  1212  2019 I ExtendedNuUtils: printFileName fd(17) -> /data/app/~~8sob2rQ304dc_blJZg7EJw==/com.bodymount.app-_dwS8VFRWAOmL9k5QVHcxw==/base.apk
08-22 16:38:19.333  1212  2019 E AudioSystem: invalid attributes { Content type: AUDIO_CONTENT_TYPE_UNKNOWN Usage: AUDIO_USAGE_UNKNOWN Source: AUDIO_SOURCE_INVALID Flags: 0x800 Tags:  } when converting to stream
08-22 16:38:19.338  1101 15569 I MMParserExtractor: Created(0xb40000751484c090)
08-22 16:38:19.339  1101 15569 D MediaBufferGroup: creating MemoryDealer
08-22 16:38:19.340 15954 15954 D DatabaseDebug: Passphrase: mykey
08-22 16:38:19.344   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_IND: 0xFD
08-22 16:38:19.344   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Writing IBS_WAKE_ACK
08-22 16:38:19.346 15954 15993 D valid   : parameter Resp
08-22 16:38:19.346  1593  2395 W bt_btm  : btm_process_inq_results: BDA: 4c:49:6c:54:ff:0f
08-22 16:38:19.346  1593  2395 W bt_btm  : btm_process_inq_results: Dev class: 2a-41-0c
08-22 16:38:19.346  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 4c:49:6c:54:ff:0f
08-22 16:38:19.346 15954 15993 D valid   : parameter PR
08-22 16:38:19.346 15954 15993 D valid   : parameter Temp
08-22 16:38:19.346 15954 15993 D valid   : parameter ConnectivityAlarm
08-22 16:38:19.346  1593  1743 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 4c:49:6c:54:ff:0f
08-22 16:38:19.346  1593  1743 D bt_btif_config: btif_get_device_type: Device [4c:49:6c:54:ff:0f] type 1
08-22 16:38:19.346  1593  1743 D BluetoothRemoteDevices: Property type: 2
08-22 16:38:19.347  1593  1743 D BluetoothRemoteDevices: Remote Address is:4C:49:6C:54:FF:0F
08-22 16:38:19.347  1593  1743 D BluetoothRemoteDevices: Property type: 1
08-22 16:38:19.347  1593  1743 D BluetoothRemoteDevices: Skip name update for 4C:49:6C:54:FF:0F
08-22 16:38:19.347  1593  1743 D BluetoothRemoteDevices: Property type: 4
08-22 16:38:19.347  1593  1743 D BluetoothRemoteDevices: Skip class update for 4C:49:6C:54:FF:0F
08-22 16:38:19.347  1593  1743 D BluetoothRemoteDevices: Property type: 5
08-22 16:38:19.347  1593  1743 D BluetoothRemoteDevices: BT_PROPERTY_TYPE_OF_DEVICE 4C:49:6C:54:FF:0F
08-22 16:38:19.347  1593  1743 D BluetoothRemoteDevices: Property type: 11
08-22 16:38:19.347  1593  1743 D BluetoothRemoteDevices: deviceFoundCallback: Remote Address is:4C:49:6C:54:FF:0F
08-22 16:38:19.386   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_SLEEP_IND: 0xFE
08-22 16:38:19.418 15954 15954 D BluetoothDiscovery: Discovered unexpected device: DESKTOP-63S9CPT, MAC: 4C:49:6C:54:FF:0F, RSSI: -82 dBm
08-22 16:38:19.428 15954 16005 I OpenGLRenderer: Davey! duration=9223349061924ms; Flags=0, FrameTimelineVsyncId=576692, IntendedVsync=22974930109279, Vsync=22974996775943, InputEventId=0, HandleInputStart=22975013425556, AnimationStart=22975013431859, PerformTraversalsStart=22975013436077, DrawStart=22975014802588, FrameDeadline=22974963442611, FrameInterval=22975013407275, FrameStartTime=16666666, SyncQueued=22975017626806, SyncStart=22975018135348, IssueDrawCommandsStart=22975018573994, SwapBuffers=22975021674723, FrameCompleted=9223372036854775807, DequeueBufferDuration=24271, QueueBufferDuration=783073, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22975022980400, DisplayPresentTime=0, 
08-22 16:38:19.444 15954 16005 I OpenGLRenderer: Davey! duration=9223349061824ms; Flags=0, FrameTimelineVsyncId=576695, IntendedVsync=22975030115929, Vsync=22975030115929, InputEventId=0, HandleInputStart=22975037671754, AnimationStart=22975037677275, PerformTraversalsStart=22975037681338, DrawStart=22975039579879, FrameDeadline=22975063449261, FrameInterval=22975037649411, FrameStartTime=16666666, SyncQueued=22975040311702, SyncStart=22975040670973, IssueDrawCommandsStart=22975041061077, SwapBuffers=22975043205921, FrameCompleted=9223372036854775807, DequeueBufferDuration=27812, QueueBufferDuration=592917, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22975044446598, DisplayPresentTime=0, 
08-22 16:38:19.470   749  2398 I vendor.qti.bluetooth@1.0-ibs_handler: DeviceSleep: TX Awake, Sending SLEEP_IND
08-22 16:38:19.470   749  2398 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK OFF
08-22 16:38:19.620   749  2099 D vendor.qti.bluetooth@1.0-wake_lock: Release wakelock is released 
08-22 16:38:20.069   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_IND: 0xFD
08-22 16:38:20.069   749  2393 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK ON
08-22 16:38:20.076   749  2393 D vendor.qti.bluetooth@1.0-wake_lock: Acquire wakelock is acquired 
08-22 16:38:20.076   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Writing IBS_WAKE_ACK
08-22 16:38:20.079  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=06:ad:20:9e:b9:fd
08-22 16:38:20.079  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=06:ad:20:9e:b9:fd
08-22 16:38:20.079  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 06:ad:20:9e:b9:fd
08-22 16:38:20.079  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=0e:7c:ce:be:dc:e7
08-22 16:38:20.079  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=0e:7c:ce:be:dc:e7
08-22 16:38:20.079  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 0e:7c:ce:be:dc:e7
08-22 16:38:20.079  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=2c:54:47:18:0a:f7
08-22 16:38:20.079  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=2c:54:47:18:0a:f7
08-22 16:38:20.079  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:20.079  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:20.079  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:20.079  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:20.079  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:20.079  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:20.079  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:20.079  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=4f:55:0d:58:a4:e1
08-22 16:38:20.079  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=4f:55:0d:58:a4:e1
08-22 16:38:20.079  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 4f:55:0d:58:a4:e1
08-22 16:38:20.079  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=4f:55:0d:58:a4:e1
08-22 16:38:20.079  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=4f:55:0d:58:a4:e1
08-22 16:38:20.079  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:20.088  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=6e:6f:f3:3b:04:96
08-22 16:38:20.088  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=6e:6f:f3:3b:04:96
08-22 16:38:20.088  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 6e:6f:f3:3b:04:96
08-22 16:38:20.094  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=22:21:1c:16:71:10
08-22 16:38:20.094  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=22:21:1c:16:71:10
08-22 16:38:20.094  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 22:21:1c:16:71:10
08-22 16:38:20.105  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=5e:b4:71:02:e3:55
08-22 16:38:20.105  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=5e:b4:71:02:e3:55
08-22 16:38:20.105  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 5e:b4:71:02:e3:55
08-22 16:38:20.107  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=fc:a8:9b:1e:87:57
08-22 16:38:20.107  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=fc:a8:9b:1e:87:57
08-22 16:38:20.107  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response fc:a8:9b:1e:87:57
08-22 16:38:20.108  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=ff:8b:e6:0b:72:a3
08-22 16:38:20.108  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=ff:8b:e6:0b:72:a3
08-22 16:38:20.108  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response ff:8b:e6:0b:72:a3
08-22 16:38:20.108  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=ff:8b:e6:0b:72:a3
08-22 16:38:20.108  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=ff:8b:e6:0b:72:a3
08-22 16:38:20.108  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA ff:8b:e6:0b:72:a3
08-22 16:38:20.111  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:20.112  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:20.112  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:20.112  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:20.112  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:20.112  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:20.114  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=25:13:9d:26:84:51
08-22 16:38:20.114  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=25:13:9d:26:84:51
08-22 16:38:20.114  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 25:13:9d:26:84:51
08-22 16:38:20.123  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=ff:8b:e6:0b:72:a3
08-22 16:38:20.123  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=ff:8b:e6:0b:72:a3
08-22 16:38:20.123  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response ff:8b:e6:0b:72:a3
08-22 16:38:20.123  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=ff:8b:e6:0b:72:a3
08-22 16:38:20.123  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=ff:8b:e6:0b:72:a3
08-22 16:38:20.123  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA ff:8b:e6:0b:72:a3
08-22 16:38:20.134  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=94:32:51:02:2c:19
08-22 16:38:20.134  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=94:32:51:02:2c:19
08-22 16:38:20.135  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 94:32:51:02:2c:19
08-22 16:38:20.136  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=44:54:82:95:10:54
08-22 16:38:20.136  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=44:54:82:95:10:54
08-22 16:38:20.136  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 44:54:82:95:10:54
08-22 16:38:20.141  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=52:b7:d9:95:37:dd
08-22 16:38:20.141  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=52:b7:d9:95:37:dd
08-22 16:38:20.141  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 52:b7:d9:95:37:dd
08-22 16:38:20.141  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=18:d6:5b:46:5a:5c
08-22 16:38:20.141  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=18:d6:5b:46:5a:5c
08-22 16:38:20.141  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:20.144  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=23:da:da:c8:2d:cf
08-22 16:38:20.144  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=23:da:da:c8:2d:cf
08-22 16:38:20.144  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 23:da:da:c8:2d:cf
08-22 16:38:20.150  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=22:68:f4:28:0f:21
08-22 16:38:20.150  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=22:68:f4:28:0f:21
08-22 16:38:20.150  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 22:68:f4:28:0f:21
08-22 16:38:20.153  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=06:ad:20:9e:b9:fd
08-22 16:38:20.153  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=06:ad:20:9e:b9:fd
08-22 16:38:20.153  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 06:ad:20:9e:b9:fd
08-22 16:38:20.164  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:20.164  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:20.164  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:20.164  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:20.164  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:20.164  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:20.169  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=0e:7c:ce:be:dc:e7
08-22 16:38:20.169  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=0e:7c:ce:be:dc:e7
08-22 16:38:20.169  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 0e:7c:ce:be:dc:e7
08-22 16:38:20.182  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=37:c5:ef:ee:53:f2
08-22 16:38:20.182  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=37:c5:ef:ee:53:f2
08-22 16:38:20.183  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:20.201  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=e2:0f:a6:c9:13:06
08-22 16:38:20.201  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=e2:0f:a6:c9:13:06
08-22 16:38:20.201  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response e2:0f:a6:c9:13:06
08-22 16:38:20.201  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=e2:0f:a6:c9:13:06
08-22 16:38:20.201  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=e2:0f:a6:c9:13:06
08-22 16:38:20.201  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:20.205  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:20.206  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:20.206  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:20.206  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:20.206  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:20.206  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:20.223  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=fc:a8:9b:1e:87:57
08-22 16:38:20.223  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=fc:a8:9b:1e:87:57
08-22 16:38:20.223  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response fc:a8:9b:1e:87:57
08-22 16:38:20.223  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=25:13:9d:26:84:51
08-22 16:38:20.223  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=25:13:9d:26:84:51
08-22 16:38:20.223  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 25:13:9d:26:84:51
08-22 16:38:20.252  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=4f:55:0d:58:a4:e1
08-22 16:38:20.253  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=4f:55:0d:58:a4:e1
08-22 16:38:20.253  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 4f:55:0d:58:a4:e1
08-22 16:38:20.253  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:20.253  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:20.253  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:20.253  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:20.253  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:20.253  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:20.273  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=0e:7c:ce:be:dc:e7
08-22 16:38:20.273  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=0e:7c:ce:be:dc:e7
08-22 16:38:20.273  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 0e:7c:ce:be:dc:e7
08-22 16:38:20.283  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=2c:54:47:18:0a:f7
08-22 16:38:20.283  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=2c:54:47:18:0a:f7
08-22 16:38:20.283  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 2c:54:47:18:0a:f7
08-22 16:38:20.292  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=52:b7:d9:95:37:dd
08-22 16:38:20.292  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=52:b7:d9:95:37:dd
08-22 16:38:20.292  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 52:b7:d9:95:37:dd
08-22 16:38:20.293  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f2:03:30:3e:10:74
08-22 16:38:20.293  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f2:03:30:3e:10:74
08-22 16:38:20.293  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f2:03:30:3e:10:74
08-22 16:38:20.293  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f2:03:30:3e:10:74
08-22 16:38:20.293  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f2:03:30:3e:10:74
08-22 16:38:20.293  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f2:03:30:3e:10:74
08-22 16:38:20.303  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:20.303  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:20.303  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:20.303  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:20.303  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:20.303  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:20.324  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=25:13:9d:26:84:51
08-22 16:38:20.324  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=25:13:9d:26:84:51
08-22 16:38:20.324  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 25:13:9d:26:84:51
08-22 16:38:20.333  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=22:21:1c:16:71:10
08-22 16:38:20.333  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=22:21:1c:16:71:10
08-22 16:38:20.333  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 22:21:1c:16:71:10
08-22 16:38:20.333  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=22:21:1c:16:71:10
08-22 16:38:20.333  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=22:21:1c:16:71:10
08-22 16:38:20.333  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 22:21:1c:16:71:10
08-22 16:38:20.334  1593  1743 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 22:21:1c:16:71:10
08-22 16:38:20.334  1593  1743 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 22:21:1c:16:71:10
08-22 16:38:20.335  1593  1743 D BluetoothRemoteDevices: Added new device property
08-22 16:38:20.336  1593  1743 D BluetoothRemoteDevices: Removing device 68:BC:E9:E5:E2:9A from property map
08-22 16:38:20.336  1593  1743 D BluetoothRemoteDevices: Property type: 2
08-22 16:38:20.336  1593  1743 D BluetoothRemoteDevices: Remote Address is:22:21:1C:16:71:10
08-22 16:38:20.336  1593  1743 D BluetoothRemoteDevices: Property type: 5
08-22 16:38:20.336  1593  1743 D BluetoothRemoteDevices: BT_PROPERTY_TYPE_OF_DEVICE 22:21:1C:16:71:10
08-22 16:38:20.336  1593  1743 D BluetoothRemoteDevices: Property type: 11
08-22 16:38:20.337  1593  1743 D BluetoothRemoteDevices: deviceFoundCallback: Remote Address is:22:21:1C:16:71:10
08-22 16:38:20.344 15954 15954 D BluetoothDiscovery: Discovered unexpected device: Unknown Device, MAC: 22:21:1C:16:71:10, RSSI: -92 dBm
08-22 16:38:20.347 15954 15993 D valid   : parameter Ecg
08-22 16:38:20.348 15954 15993 D valid   : parameter BpSys
08-22 16:38:20.348 15954 15993 D valid   : parameter Fall
08-22 16:38:20.348 15954 15993 D valid   : parameter BpDia
08-22 16:38:20.348 15954 15993 D valid   : parameter Spo2
08-22 16:38:20.348 15954 15993 D valid   : parameter Resp
08-22 16:38:20.348 15954 15993 D valid   : parameter PR
08-22 16:38:20.348 15954 15993 D valid   : parameter Temp
08-22 16:38:20.348 15954 15993 D valid   : parameter ConnectivityAlarm
08-22 16:38:20.373   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_SLEEP_IND: 0xFE
08-22 16:38:20.373   749  2393 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK OFF
08-22 16:38:20.449 15954 15954 D DatabaseDebug: Passphrase: mykey
08-22 16:38:20.504 15954 15967 W MediaPlayer-JNI: MediaPlayer finalized without being released
08-22 16:38:20.505  1593  2683 D Avrcp_ext: AudioManager Player: AudioPlaybackConfiguration piid:79 deviceId:0 type:android.media.SoundPool u/pid:1000/1359 state:idle attr:AudioAttributes: usage=USAGE_ASSISTANCE_SONIFICATION content=CONTENT_TYPE_SONIFICATION flags=0x800 tags= bundle=null sessionId:0
08-22 16:38:20.505  1593  2683 D Avrcp_ext: AudioManager Player: AudioPlaybackConfiguration piid:87 deviceId:0 type:android.media.SoundPool u/pid:10089/1609 state:idle attr:AudioAttributes: usage=USAGE_ASSISTANCE_SONIFICATION content=CONTENT_TYPE_SONIFICATION flags=0x800 tags= bundle=null sessionId:0
08-22 16:38:20.505  1593  2683 D Avrcp_ext: AudioManager Player: AudioPlaybackConfiguration piid:95 deviceId:0 type:android.media.SoundPool u/pid:1027/2413 state:idle attr:AudioAttributes: usage=USAGE_ASSISTANCE_SONIFICATION content=CONTENT_TYPE_SONIFICATION flags=0x800 tags= bundle=null sessionId:0
08-22 16:38:20.505  1593  2683 D Avrcp_ext: AudioManager Player: AudioPlaybackConfiguration piid:6063 deviceId:0 type:android.media.MediaPlayer u/pid:10223/15954 state:idle attr:AudioAttributes: usage=USAGE_UNKNOWN content=CONTENT_TYPE_UNKNOWN flags=0x800 tags= bundle=null sessionId:11809
08-22 16:38:20.505  1593  2683 D Avrcp_ext: AudioManager isPlaying: false, mAudioPlaybackIsActive = false
08-22 16:38:20.505  1593  2683 D Avrcp_ext: AudioManager Reset Active Player
08-22 16:38:20.508  1212  2019 V AudioSink: close
08-22 16:38:20.523   749  2099 D vendor.qti.bluetooth@1.0-wake_lock: Release wakelock is released 
08-22 16:38:20.680 15954 16005 I OpenGLRenderer: Davey! duration=9223349060590ms; Flags=0, FrameTimelineVsyncId=576736, IntendedVsync=22976263471020, Vsync=22976263471020, InputEventId=0, HandleInputStart=22976265694879, AnimationStart=22976265706493, PerformTraversalsStart=22976265712066, DrawStart=22976266081650, FrameDeadline=22976280137686, FrameInterval=22976265659566, FrameStartTime=16666666, SyncQueued=22976267171337, SyncStart=22976267498525, IssueDrawCommandsStart=22976267765452, SwapBuffers=22976271450556, FrameCompleted=9223372036854775807, DequeueBufferDuration=49062, QueueBufferDuration=1027552, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22976273177900, DisplayPresentTime=0, 
08-22 16:38:21.350 15954 15996 D valid   : parameter Ecg
08-22 16:38:21.350 15954 15996 D valid   : parameter BpSys
08-22 16:38:21.350 15954 15996 D valid   : parameter Fall
08-22 16:38:21.350 15954 15996 D valid   : parameter BpDia
08-22 16:38:21.351 15954 15996 D valid   : parameter Spo2
08-22 16:38:21.351 15954 15996 D valid   : parameter Resp
08-22 16:38:21.351 15954 15996 D valid   : parameter PR
08-22 16:38:21.351 15954 15996 D valid   : parameter Temp
08-22 16:38:21.351 15954 15996 D valid   : parameter ConnectivityAlarm
08-22 16:38:21.536 15954 15954 D DatabaseDebug: Passphrase: mykey
08-22 16:38:21.669   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_IND: 0xFD
08-22 16:38:21.669   749  2393 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK ON
08-22 16:38:21.672   749  2393 D vendor.qti.bluetooth@1.0-wake_lock: Acquire wakelock is acquired 
08-22 16:38:21.672   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Writing IBS_WAKE_ACK
08-22 16:38:21.675  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=22:21:1c:16:71:10
08-22 16:38:21.675  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=22:21:1c:16:71:10
08-22 16:38:21.675  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 22:21:1c:16:71:10
08-22 16:38:21.675  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=22:21:1c:16:71:10
08-22 16:38:21.675  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=22:21:1c:16:71:10
08-22 16:38:21.675  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 22:21:1c:16:71:10
08-22 16:38:21.675  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=94:32:51:02:2c:19
08-22 16:38:21.675  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=94:32:51:02:2c:19
08-22 16:38:21.675  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 94:32:51:02:2c:19
08-22 16:38:21.675  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=94:32:51:02:2c:19
08-22 16:38:21.675  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=94:32:51:02:2c:19
08-22 16:38:21.675  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 94:32:51:02:2c:19
08-22 16:38:21.675  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=44:54:82:95:10:54
08-22 16:38:21.675  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=44:54:82:95:10:54
08-22 16:38:21.675  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 44:54:82:95:10:54
08-22 16:38:21.675  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:21.675  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:21.675  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:21.676  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:21.676  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:21.676  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:21.676  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=25:13:9d:26:84:51
08-22 16:38:21.676  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=25:13:9d:26:84:51
08-22 16:38:21.676  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 25:13:9d:26:84:51
08-22 16:38:21.697  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=e5:53:7c:25:b6:37
08-22 16:38:21.697  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=e5:53:7c:25:b6:37
08-22 16:38:21.697  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response e5:53:7c:25:b6:37
08-22 16:38:21.698  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=e5:53:7c:25:b6:37
08-22 16:38:21.698  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=e5:53:7c:25:b6:37
08-22 16:38:21.698  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA e5:53:7c:25:b6:37
08-22 16:38:21.705  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:21.705  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:21.705  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:21.705  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:21.705  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:21.705  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:21.709  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=6e:6f:f3:3b:04:96
08-22 16:38:21.709  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=6e:6f:f3:3b:04:96
08-22 16:38:21.709  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 6e:6f:f3:3b:04:96
08-22 16:38:21.709  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=6e:6f:f3:3b:04:96
08-22 16:38:21.709  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=6e:6f:f3:3b:04:96
08-22 16:38:21.709  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 6e:6f:f3:3b:04:96
08-22 16:38:21.709  1593  1743 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 6e:6f:f3:3b:04:96
08-22 16:38:21.710  1593  1743 D bt_btif_config: btif_get_device_type: Device [6e:6f:f3:3b:04:96] type 2
08-22 16:38:21.711  1593  1743 D BluetoothRemoteDevices: Property type: 2
08-22 16:38:21.711  1593  1743 D BluetoothRemoteDevices: Remote Address is:6E:6F:F3:3B:04:96
08-22 16:38:21.711  1593  1743 D BluetoothRemoteDevices: Property type: 5
08-22 16:38:21.711  1593  1743 D BluetoothRemoteDevices: BT_PROPERTY_TYPE_OF_DEVICE 6E:6F:F3:3B:04:96
08-22 16:38:21.711  1593  1743 D BluetoothRemoteDevices: Property type: 11
08-22 16:38:21.711  1593  1743 D BluetoothRemoteDevices: deviceFoundCallback: Remote Address is:6E:6F:F3:3B:04:96
08-22 16:38:21.712  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=22:68:f4:28:0f:21
08-22 16:38:21.712  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=22:68:f4:28:0f:21
08-22 16:38:21.713  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 22:68:f4:28:0f:21
08-22 16:38:21.717 15954 15954 D BluetoothDiscovery: Discovered unexpected device: Unknown Device, MAC: 6E:6F:F3:3B:04:96, RSSI: -98 dBm
08-22 16:38:21.750  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=2c:54:47:18:0a:f7
08-22 16:38:21.750  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=2c:54:47:18:0a:f7
08-22 16:38:21.750  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 2c:54:47:18:0a:f7
08-22 16:38:21.752  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:21.752  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:21.752  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:21.752  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:21.752  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:21.752  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:21.763  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=5e:ac:ee:80:be:93
08-22 16:38:21.763  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=5e:ac:ee:80:be:93
08-22 16:38:21.763  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 5e:ac:ee:80:be:93
08-22 16:38:21.763  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=5e:ac:ee:80:be:93
08-22 16:38:21.763  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=5e:ac:ee:80:be:93
08-22 16:38:21.763  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 5e:ac:ee:80:be:93
08-22 16:38:21.771  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=5c:f2:0c:4b:45:83
08-22 16:38:21.771  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=5c:f2:0c:4b:45:83
08-22 16:38:21.771  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 5c:f2:0c:4b:45:83
08-22 16:38:21.771  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=5c:f2:0c:4b:45:83
08-22 16:38:21.771  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=5c:f2:0c:4b:45:83
08-22 16:38:21.771  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 5c:f2:0c:4b:45:83
08-22 16:38:21.773  1593  1743 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 5c:f2:0c:4b:45:83
08-22 16:38:21.774  1593  1743 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 5c:f2:0c:4b:45:83
08-22 16:38:21.775  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=25:13:9d:26:84:51
08-22 16:38:21.775  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=25:13:9d:26:84:51
08-22 16:38:21.775  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 25:13:9d:26:84:51
08-22 16:38:21.775  1593  1743 D BluetoothRemoteDevices: Added new device property
08-22 16:38:21.776  1593  1743 D BluetoothRemoteDevices: Removing device 6E:6F:F3:3B:04:96 from property map
08-22 16:38:21.776  1593  1743 D BluetoothRemoteDevices: Property type: 2
08-22 16:38:21.776  1593  1743 D BluetoothRemoteDevices: Remote Address is:5C:F2:0C:4B:45:83
08-22 16:38:21.776  1593  1743 D BluetoothRemoteDevices: Property type: 5
08-22 16:38:21.776  1593  1743 D BluetoothRemoteDevices: BT_PROPERTY_TYPE_OF_DEVICE 5C:F2:0C:4B:45:83
08-22 16:38:21.777  1593  1743 D BluetoothRemoteDevices: Property type: 11
08-22 16:38:21.777  1593  1743 D BluetoothRemoteDevices: deviceFoundCallback: Remote Address is:5C:F2:0C:4B:45:83
08-22 16:38:21.783 15954 15954 D BluetoothDiscovery: Discovered unexpected device: Unknown Device, MAC: 5C:F2:0C:4B:45:83, RSSI: -98 dBm
08-22 16:38:21.813  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=fc:a8:9b:1e:87:57
08-22 16:38:21.813  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=fc:a8:9b:1e:87:57
08-22 16:38:21.813  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response fc:a8:9b:1e:87:57
08-22 16:38:21.813  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=22:68:f4:28:0f:21
08-22 16:38:21.813  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=22:68:f4:28:0f:21
08-22 16:38:21.813  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 22:68:f4:28:0f:21
08-22 16:38:21.818  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f2:03:30:3e:10:74
08-22 16:38:21.818  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f2:03:30:3e:10:74
08-22 16:38:21.818  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f2:03:30:3e:10:74
08-22 16:38:21.818  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f2:03:30:3e:10:74
08-22 16:38:21.818  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f2:03:30:3e:10:74
08-22 16:38:21.818  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f2:03:30:3e:10:74
08-22 16:38:21.836  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=23:da:da:c8:2d:cf
08-22 16:38:21.836  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=23:da:da:c8:2d:cf
08-22 16:38:21.836  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 23:da:da:c8:2d:cf
08-22 16:38:21.836  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=06:ad:20:9e:b9:fd
08-22 16:38:21.836  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=06:ad:20:9e:b9:fd
08-22 16:38:21.836  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 06:ad:20:9e:b9:fd
08-22 16:38:21.839  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:21.839  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:21.839  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:21.839  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:21.839  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:21.839  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:21.839  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=0e:7c:ce:be:dc:e7
08-22 16:38:21.839  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=0e:7c:ce:be:dc:e7
08-22 16:38:21.839  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 0e:7c:ce:be:dc:e7
08-22 16:38:21.844  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=e3:31:77:8d:54:26
08-22 16:38:21.844  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=e3:31:77:8d:54:26
08-22 16:38:21.844  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:21.848  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=d8:b2:4d:09:ff:07
08-22 16:38:21.848  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=d8:b2:4d:09:ff:07
08-22 16:38:21.848  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:21.850  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=3a:3e:4c:db:1c:5e
08-22 16:38:21.850  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=3a:3e:4c:db:1c:5e
08-22 16:38:21.850  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 3a:3e:4c:db:1c:5e
08-22 16:38:21.854  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=52:b7:d9:95:37:dd
08-22 16:38:21.854  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=52:b7:d9:95:37:dd
08-22 16:38:21.854  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 52:b7:d9:95:37:dd
08-22 16:38:21.854  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=52:b7:d9:95:37:dd
08-22 16:38:21.854  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=52:b7:d9:95:37:dd
08-22 16:38:21.855  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 52:b7:d9:95:37:dd
08-22 16:38:21.858  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=2c:54:47:18:0a:f7
08-22 16:38:21.858  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=2c:54:47:18:0a:f7
08-22 16:38:21.858  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 2c:54:47:18:0a:f7
08-22 16:38:21.878  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=25:13:9d:26:84:51
08-22 16:38:21.878  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=25:13:9d:26:84:51
08-22 16:38:21.878  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 25:13:9d:26:84:51
08-22 16:38:21.897  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=20:df:3c:ec:55:cc
08-22 16:38:21.897  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=20:df:3c:ec:55:cc
08-22 16:38:21.897  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:21.902  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=4f:55:0d:58:a4:e1
08-22 16:38:21.902  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=4f:55:0d:58:a4:e1
08-22 16:38:21.902  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 4f:55:0d:58:a4:e1
08-22 16:38:21.902  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=4f:55:0d:58:a4:e1
08-22 16:38:21.902  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=4f:55:0d:58:a4:e1
08-22 16:38:21.902  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 4f:55:0d:58:a4:e1
08-22 16:38:21.921  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=d6:33:1c:d1:03:f6
08-22 16:38:21.921  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=d6:33:1c:d1:03:f6
08-22 16:38:21.921  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response d6:33:1c:d1:03:f6
08-22 16:38:21.921  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:21.921  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:21.922  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:21.922  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:21.922  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:21.922  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:21.924  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=51:1e:c0:95:11:11
08-22 16:38:21.924  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=51:1e:c0:95:11:11
08-22 16:38:21.924  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 51:1e:c0:95:11:11
08-22 16:38:21.924  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=51:1e:c0:95:11:11
08-22 16:38:21.924  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=51:1e:c0:95:11:11
08-22 16:38:21.924  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:21.926  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=22:21:1c:16:71:10
08-22 16:38:21.926  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=22:21:1c:16:71:10
08-22 16:38:21.926  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 22:21:1c:16:71:10
08-22 16:38:21.926  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=22:21:1c:16:71:10
08-22 16:38:21.926  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=22:21:1c:16:71:10
08-22 16:38:21.926  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 22:21:1c:16:71:10
08-22 16:38:21.939  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=47:ff:28:9d:94:66
08-22 16:38:21.939  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=47:ff:28:9d:94:66
08-22 16:38:21.939  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 47:ff:28:9d:94:66
08-22 16:38:21.940  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=47:ff:28:9d:94:66
08-22 16:38:21.940  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=47:ff:28:9d:94:66
08-22 16:38:21.940  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:21.940  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=23:da:da:c8:2d:cf
08-22 16:38:21.940  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=23:da:da:c8:2d:cf
08-22 16:38:21.940  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 23:da:da:c8:2d:cf
08-22 16:38:21.942  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=06:ad:20:9e:b9:fd
08-22 16:38:21.942  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=06:ad:20:9e:b9:fd
08-22 16:38:21.942  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 06:ad:20:9e:b9:fd
08-22 16:38:21.947  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=0e:7c:ce:be:dc:e7
08-22 16:38:21.947  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=0e:7c:ce:be:dc:e7
08-22 16:38:21.947  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 0e:7c:ce:be:dc:e7
08-22 16:38:21.957  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=94:32:51:02:2c:19
08-22 16:38:21.957  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=94:32:51:02:2c:19
08-22 16:38:21.957  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 94:32:51:02:2c:19
08-22 16:38:21.957  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=94:32:51:02:2c:19
08-22 16:38:21.957  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=94:32:51:02:2c:19
08-22 16:38:21.957  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 94:32:51:02:2c:19
08-22 16:38:21.997   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_SLEEP_IND: 0xFE
08-22 16:38:21.997   749  2393 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK OFF
08-22 16:38:22.147   749  2099 D vendor.qti.bluetooth@1.0-wake_lock: Release wakelock is released 
08-22 16:38:22.353 15954 15996 D valid   : parameter Ecg
08-22 16:38:22.353 15954 15996 D valid   : parameter BpSys
08-22 16:38:22.353 15954 15996 D valid   : parameter Fall
08-22 16:38:22.353 15954 15996 D valid   : parameter BpDia
08-22 16:38:22.353 15954 15996 D valid   : parameter Spo2
08-22 16:38:22.353 15954 15996 D valid   : parameter Resp
08-22 16:38:22.353 15954 15996 D valid   : parameter PR
08-22 16:38:22.354 15954 15996 D valid   : parameter Temp
08-22 16:38:22.354 15954 15996 D valid   : parameter ConnectivityAlarm
08-22 16:38:22.626 15954 15954 D DatabaseDebug: Passphrase: mykey
08-22 16:38:22.711 15954 16005 I OpenGLRenderer: Davey! duration=9223349058641ms; Flags=0, FrameTimelineVsyncId=576748, IntendedVsync=22978213107393, Vsync=22978296440723, InputEventId=0, HandleInputStart=22978297710295, AnimationStart=22978297726493, PerformTraversalsStart=22978297735191, DrawStart=22978298943472, FrameDeadline=22978229774059, FrameInterval=22978297654722, FrameStartTime=16666666, SyncQueued=22978301541909, SyncStart=22978301774618, IssueDrawCommandsStart=22978301973524, SwapBuffers=22978304213420, FrameCompleted=9223372036854775807, DequeueBufferDuration=25729, QueueBufferDuration=541250, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22978305160086, DisplayPresentTime=0, 
08-22 16:38:22.729 15954 16005 I OpenGLRenderer: Davey! duration=9223349058541ms; Flags=0, FrameTimelineVsyncId=576751, IntendedVsync=22978313102141, Vsync=22978313102141, InputEventId=0, HandleInputStart=22978320354670, AnimationStart=22978320360659, PerformTraversalsStart=22978320365086, DrawStart=22978321860243, FrameDeadline=22978346435473, FrameInterval=22978320327326, FrameStartTime=16666666, SyncQueued=22978322591701, SyncStart=22978322767222, IssueDrawCommandsStart=22978322914201, SwapBuffers=22978325104566, FrameCompleted=9223372036854775807, DequeueBufferDuration=24427, QueueBufferDuration=1034584, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22978326698420, DisplayPresentTime=0, 
08-22 16:38:23.272   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_IND: 0xFD
08-22 16:38:23.272   749  2393 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK ON
08-22 16:38:23.274   749  2393 D vendor.qti.bluetooth@1.0-wake_lock: Acquire wakelock is acquired 
08-22 16:38:23.274   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Writing IBS_WAKE_ACK
08-22 16:38:23.277  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=e2:0f:a6:c9:13:06
08-22 16:38:23.277  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=e2:0f:a6:c9:13:06
08-22 16:38:23.277  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response e2:0f:a6:c9:13:06
08-22 16:38:23.277  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=e2:0f:a6:c9:13:06
08-22 16:38:23.277  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=e2:0f:a6:c9:13:06
08-22 16:38:23.277  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA e2:0f:a6:c9:13:06
08-22 16:38:23.277  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:23.277  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:23.277  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:23.277  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:23.277  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:23.277  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:23.278  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=22:68:f4:28:0f:21
08-22 16:38:23.278  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=22:68:f4:28:0f:21
08-22 16:38:23.278  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 22:68:f4:28:0f:21
08-22 16:38:23.288  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=0e:7c:ce:be:dc:e7
08-22 16:38:23.288  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=0e:7c:ce:be:dc:e7
08-22 16:38:23.288  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 0e:7c:ce:be:dc:e7
08-22 16:38:23.303  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=23:da:da:c8:2d:cf
08-22 16:38:23.303  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=23:da:da:c8:2d:cf
08-22 16:38:23.303  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 23:da:da:c8:2d:cf
08-22 16:38:23.317  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=b0:10:a0:f5:e3:eb
08-22 16:38:23.317  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=b0:10:a0:f5:e3:eb
08-22 16:38:23.317  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response b0:10:a0:f5:e3:eb
08-22 16:38:23.317  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:23.317  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:23.317  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:23.317  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:23.317  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:23.317  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:23.326  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=06:ad:20:9e:b9:fd
08-22 16:38:23.327  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=06:ad:20:9e:b9:fd
08-22 16:38:23.327  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 06:ad:20:9e:b9:fd
08-22 16:38:23.330  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=25:13:9d:26:84:51
08-22 16:38:23.330  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=25:13:9d:26:84:51
08-22 16:38:23.330  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 25:13:9d:26:84:51
08-22 16:38:23.354  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=8c:fd:15:da:62:ed
08-22 16:38:23.354  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=8c:fd:15:da:62:ed
08-22 16:38:23.354  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 8c:fd:15:da:62:ed
08-22 16:38:23.356 15954 15996 D valid   : parameter Ecg
08-22 16:38:23.356 15954 15996 D valid   : parameter BpSys
08-22 16:38:23.356 15954 15996 D valid   : parameter Fall
08-22 16:38:23.356 15954 15996 D valid   : parameter BpDia
08-22 16:38:23.357 15954 15996 D valid   : parameter Spo2
08-22 16:38:23.357  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f2:03:30:3e:10:74
08-22 16:38:23.357 15954 15996 D valid   : parameter Resp
08-22 16:38:23.357  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f2:03:30:3e:10:74
08-22 16:38:23.357  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f2:03:30:3e:10:74
08-22 16:38:23.357 15954 15996 D valid   : parameter PR
08-22 16:38:23.357 15954 15996 D valid   : parameter Temp
08-22 16:38:23.357 15954 15996 D valid   : parameter ConnectivityAlarm
08-22 16:38:23.358  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:23.358  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:23.358  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:23.359  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:23.359  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:23.359  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:23.379  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=22:68:f4:28:0f:21
08-22 16:38:23.379  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=22:68:f4:28:0f:21
08-22 16:38:23.379  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 22:68:f4:28:0f:21
08-22 16:38:23.392  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=5e:b4:71:02:e3:55
08-22 16:38:23.392  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=5e:b4:71:02:e3:55
08-22 16:38:23.392  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 5e:b4:71:02:e3:55
08-22 16:38:23.392  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=0e:7c:ce:be:dc:e7
08-22 16:38:23.392  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=0e:7c:ce:be:dc:e7
08-22 16:38:23.392  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 0e:7c:ce:be:dc:e7
08-22 16:38:23.399  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:23.399  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:23.399  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:23.399  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:23.400  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:23.400  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:23.401  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=2c:54:47:18:0a:f7
08-22 16:38:23.401  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=2c:54:47:18:0a:f7
08-22 16:38:23.401  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 2c:54:47:18:0a:f7
08-22 16:38:23.409  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=52:b7:d9:95:37:dd
08-22 16:38:23.410  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=52:b7:d9:95:37:dd
08-22 16:38:23.410  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 52:b7:d9:95:37:dd
08-22 16:38:23.410  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=52:b7:d9:95:37:dd
08-22 16:38:23.410  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=52:b7:d9:95:37:dd
08-22 16:38:23.410  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 52:b7:d9:95:37:dd
08-22 16:38:23.410  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=23:da:da:c8:2d:cf
08-22 16:38:23.410  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=23:da:da:c8:2d:cf
08-22 16:38:23.410  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 23:da:da:c8:2d:cf
08-22 16:38:23.417  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=5e:ac:ee:80:be:93
08-22 16:38:23.417  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=5e:ac:ee:80:be:93
08-22 16:38:23.417  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 5e:ac:ee:80:be:93
08-22 16:38:23.417  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=5e:ac:ee:80:be:93
08-22 16:38:23.417  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=5e:ac:ee:80:be:93
08-22 16:38:23.417  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 5e:ac:ee:80:be:93
08-22 16:38:23.426  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=d6:33:1c:d1:03:f6
08-22 16:38:23.426  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=d6:33:1c:d1:03:f6
08-22 16:38:23.426  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response d6:33:1c:d1:03:f6
08-22 16:38:23.426  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=d6:33:1c:d1:03:f6
08-22 16:38:23.426  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=d6:33:1c:d1:03:f6
08-22 16:38:23.426  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr d6:33:1c:d1:03:f6
08-22 16:38:23.426  1593  1743 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr d6:33:1c:d1:03:f6
08-22 16:38:23.427  1593  1743 D bt_btif_config: btif_get_device_type: Device [d6:33:1c:d1:03:f6] type 2
08-22 16:38:23.427  1593  1743 D BluetoothRemoteDevices: Property type: 2
08-22 16:38:23.427  1593  1743 D BluetoothRemoteDevices: Remote Address is:D6:33:1C:D1:03:F6
08-22 16:38:23.427  1593  1743 D BluetoothRemoteDevices: Property type: 1
08-22 16:38:23.427  1593  1743 D BluetoothRemoteDevices: Skip name update for D6:33:1C:D1:03:F6
08-22 16:38:23.427  1593  1743 D BluetoothRemoteDevices: Property type: 5
08-22 16:38:23.427  1593  1743 D BluetoothRemoteDevices: BT_PROPERTY_TYPE_OF_DEVICE D6:33:1C:D1:03:F6
08-22 16:38:23.427  1593  1743 D BluetoothRemoteDevices: Property type: 11
08-22 16:38:23.427  1593  1743 D BluetoothRemoteDevices: deviceFoundCallback: Remote Address is:D6:33:1C:D1:03:F6
08-22 16:38:23.432  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=06:ad:20:9e:b9:fd
08-22 16:38:23.433  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=06:ad:20:9e:b9:fd
08-22 16:38:23.433  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 06:ad:20:9e:b9:fd
08-22 16:38:23.435  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=25:13:9d:26:84:51
08-22 16:38:23.435  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=25:13:9d:26:84:51
08-22 16:38:23.435  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 25:13:9d:26:84:51
08-22 16:38:23.437 15954 15954 D BluetoothDiscovery: Discovered unexpected device: ColorFit Pulse 3_03F6, MAC: D6:33:1C:D1:03:F6, RSSI: -74 dBm
08-22 16:38:23.445  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:23.445  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:23.445  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:23.446  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:23.446  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:23.446  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:23.455  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=8c:fd:15:da:62:ed
08-22 16:38:23.455  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=8c:fd:15:da:62:ed
08-22 16:38:23.456  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 8c:fd:15:da:62:ed
08-22 16:38:23.456  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=8c:fd:15:da:62:ed
08-22 16:38:23.456  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=8c:fd:15:da:62:ed
08-22 16:38:23.456  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:23.487  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=e2:50:9f:15:8f:9c
08-22 16:38:23.487  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=e2:50:9f:15:8f:9c
08-22 16:38:23.487  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response e2:50:9f:15:8f:9c
08-22 16:38:23.487  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=22:68:f4:28:0f:21
08-22 16:38:23.487  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=22:68:f4:28:0f:21
08-22 16:38:23.487  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 22:68:f4:28:0f:21
08-22 16:38:23.501  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=0e:7c:ce:be:dc:e7
08-22 16:38:23.501  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=0e:7c:ce:be:dc:e7
08-22 16:38:23.501  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 0e:7c:ce:be:dc:e7
08-22 16:38:23.540   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_SLEEP_IND: 0xFE
08-22 16:38:23.540   749  2393 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK OFF
08-22 16:38:23.690   749  2099 D vendor.qti.bluetooth@1.0-wake_lock: Release wakelock is released 
08-22 16:38:23.735 15954 15954 D DatabaseDebug: Passphrase: mykey
08-22 16:38:23.900   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_IND: 0xFD
08-22 16:38:23.900   749  2393 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK ON
08-22 16:38:23.905   749  2393 D vendor.qti.bluetooth@1.0-wake_lock: Acquire wakelock is acquired 
08-22 16:38:23.905   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Writing IBS_WAKE_ACK
08-22 16:38:23.908  1593  2395 W bt_btm  : btm_process_inq_results: BDA: 48:74:12:36:51:b6
08-22 16:38:23.908  1593  2395 W bt_btm  : btm_process_inq_results: Dev class: 5a-02-0c
08-22 16:38:23.908  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 48:74:12:36:51:b6
08-22 16:38:23.909  1593  1743 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 48:74:12:36:51:b6
08-22 16:38:23.911  1593  1743 D BluetoothRemoteDevices: Added new device property
08-22 16:38:23.912  1593  1743 D BluetoothRemoteDevices: Removing device 47:80:23:B1:8E:3B from property map
08-22 16:38:23.912  1593  1743 D BluetoothRemoteDevices: Property type: 2
08-22 16:38:23.912  1593  1743 D BluetoothRemoteDevices: Remote Address is:48:74:12:36:51:B6
08-22 16:38:23.912  1593  1743 D BluetoothRemoteDevices: Property type: 1
08-22 16:38:23.914  1593  1743 I BluetoothAdapterService:  isIgnoreDevice false device name null addr 48:74:12:36:51:B6
08-22 16:38:23.916  1593  1743 D BluetoothRemoteDevices: Remote Device name is: OnePlus 9RT 5G
08-22 16:38:23.916  1593  1743 D BluetoothRemoteDevices: Property type: 4
08-22 16:38:23.916  1593  1743 D BluetoothRemoteDevices: Remote after adv audio class is:7936 48:74:12:36:51:B6
08-22 16:38:23.917  1593  1743 D BluetoothRemoteDevices: Remote class is:5898764
08-22 16:38:23.917  1593  1743 D BluetoothRemoteDevices: Property type: 5
08-22 16:38:23.917  1593  1743 D BluetoothRemoteDevices: BT_PROPERTY_TYPE_OF_DEVICE 48:74:12:36:51:B6
08-22 16:38:23.917  1593  1743 D BluetoothRemoteDevices: Property type: 11
08-22 16:38:23.917  1593  1743 D BluetoothRemoteDevices: deviceFoundCallback: Remote Address is:48:74:12:36:51:B6
08-22 16:38:23.923 15954 15954 D BluetoothDiscovery: Discovered unexpected device: OnePlus 9RT 5G, MAC: 48:74:12:36:51:B6, RSSI: -86 dBm
08-22 16:38:23.947   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_SLEEP_IND: 0xFE
08-22 16:38:23.947   749  2393 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK OFF
08-22 16:38:24.098   749  2099 D vendor.qti.bluetooth@1.0-wake_lock: Release wakelock is released 
08-22 16:38:24.161 15954 16005 I OpenGLRenderer: Davey! duration=9223349057107ms; Flags=0, FrameTimelineVsyncId=576791, IntendedVsync=22979747048087, Vsync=22979747048087, InputEventId=0, HandleInputStart=22979752391805, AnimationStart=22979752398836, PerformTraversalsStart=22979752403732, DrawStart=22979755440711, FrameDeadline=22979763714753, FrameInterval=22979752368107, FrameStartTime=16666666, SyncQueued=22979756195971, SyncStart=22979756374825, IssueDrawCommandsStart=22979756582378, SwapBuffers=22979759075242, FrameCompleted=9223372036854775807, DequeueBufferDuration=30468, QueueBufferDuration=825053, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22979760463055, DisplayPresentTime=0, 
08-22 16:38:24.359 15954 15996 D valid   : parameter Ecg
08-22 16:38:24.359 15954 15996 D valid   : parameter BpSys
08-22 16:38:24.359 15954 15996 D valid   : parameter Fall
08-22 16:38:24.359 15954 15996 D valid   : parameter BpDia
08-22 16:38:24.359 15954 15996 D valid   : parameter Spo2
08-22 16:38:24.360 15954 15996 D valid   : parameter Resp
08-22 16:38:24.360 15954 15996 D valid   : parameter PR
08-22 16:38:24.360 15954 15996 D valid   : parameter Temp
08-22 16:38:24.360 15954 15996 D valid   : parameter ConnectivityAlarm
08-22 16:38:24.834 15954 15954 D DatabaseDebug: Passphrase: mykey
08-22 16:38:24.851   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_IND: 0xFD
08-22 16:38:24.851   749  2393 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK ON
08-22 16:38:24.854   749  2393 D vendor.qti.bluetooth@1.0-wake_lock: Acquire wakelock is acquired 
08-22 16:38:24.854   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Writing IBS_WAKE_ACK
08-22 16:38:24.856  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=22:68:f4:28:0f:21
08-22 16:38:24.857  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=22:68:f4:28:0f:21
08-22 16:38:24.857  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 22:68:f4:28:0f:21
08-22 16:38:24.857  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:24.857  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:24.857  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:24.857  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:24.857  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:24.857  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:24.857  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=0e:7c:ce:be:dc:e7
08-22 16:38:24.857  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=0e:7c:ce:be:dc:e7
08-22 16:38:24.857  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 0e:7c:ce:be:dc:e7
08-22 16:38:24.873  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f2:03:30:3e:10:74
08-22 16:38:24.873  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f2:03:30:3e:10:74
08-22 16:38:24.873  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f2:03:30:3e:10:74
08-22 16:38:24.873  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f2:03:30:3e:10:74
08-22 16:38:24.873  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f2:03:30:3e:10:74
08-22 16:38:24.873  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f2:03:30:3e:10:74
08-22 16:38:24.888  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=4f:55:0d:58:a4:e1
08-22 16:38:24.888  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=4f:55:0d:58:a4:e1
08-22 16:38:24.888  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 4f:55:0d:58:a4:e1
08-22 16:38:24.889  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:24.889  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:24.889  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:24.889  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:24.889  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:24.890  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:24.910  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=25:13:9d:26:84:51
08-22 16:38:24.910  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=25:13:9d:26:84:51
08-22 16:38:24.910  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 25:13:9d:26:84:51
08-22 16:38:24.924  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=66:05:af:71:23:55
08-22 16:38:24.924  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=66:05:af:71:23:55
08-22 16:38:24.924  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 66:05:af:71:23:55
08-22 16:38:24.924  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=c7:1c:d9:28:26:34
08-22 16:38:24.924  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=c7:1c:d9:28:26:34
08-22 16:38:24.924  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:24.933  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:24.933  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:24.933  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:24.933  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:24.933  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:24.933  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:24.940  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=5d:e1:1e:ff:f5:ab
08-22 16:38:24.940  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=5d:e1:1e:ff:f5:ab
08-22 16:38:24.940  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 5d:e1:1e:ff:f5:ab
08-22 16:38:24.940  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=22:68:f4:28:0f:21
08-22 16:38:24.940  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=22:68:f4:28:0f:21
08-22 16:38:24.940  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 22:68:f4:28:0f:21
08-22 16:38:24.949  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=d6:33:1c:d1:03:f6
08-22 16:38:24.950  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=d6:33:1c:d1:03:f6
08-22 16:38:24.950  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response d6:33:1c:d1:03:f6
08-22 16:38:24.950  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=d6:33:1c:d1:03:f6
08-22 16:38:24.950  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=d6:33:1c:d1:03:f6
08-22 16:38:24.950  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA d6:33:1c:d1:03:f6
08-22 16:38:24.958  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=6e:6f:f3:3b:04:96
08-22 16:38:24.958  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=6e:6f:f3:3b:04:96
08-22 16:38:24.958  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 6e:6f:f3:3b:04:96
08-22 16:38:24.958  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=6e:6f:f3:3b:04:96
08-22 16:38:24.958  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=6e:6f:f3:3b:04:96
08-22 16:38:24.958  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 6e:6f:f3:3b:04:96
08-22 16:38:24.977  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=52:b7:d9:95:37:dd
08-22 16:38:24.977  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=52:b7:d9:95:37:dd
08-22 16:38:24.977  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 52:b7:d9:95:37:dd
08-22 16:38:24.977  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=23:da:da:c8:2d:cf
08-22 16:38:24.977  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=23:da:da:c8:2d:cf
08-22 16:38:24.977  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 23:da:da:c8:2d:cf
08-22 16:38:24.980  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:24.980  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:24.980  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:24.980  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:24.980  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:24.981  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:24.983  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=06:ad:20:9e:b9:fd
08-22 16:38:24.983  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=06:ad:20:9e:b9:fd
08-22 16:38:24.983  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 06:ad:20:9e:b9:fd
08-22 16:38:25.022   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_SLEEP_IND: 0xFE
08-22 16:38:25.022   749  2393 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK OFF
08-22 16:38:25.035   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_IND: 0xFD
08-22 16:38:25.035   749  2393 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK ON
08-22 16:38:25.035   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Writing IBS_WAKE_ACK
08-22 16:38:25.038  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=94:32:51:02:2c:19
08-22 16:38:25.039  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=94:32:51:02:2c:19
08-22 16:38:25.039  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 94:32:51:02:2c:19
08-22 16:38:25.039  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=5e:b4:71:02:e3:55
08-22 16:38:25.039  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=5e:b4:71:02:e3:55
08-22 16:38:25.039  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 5e:b4:71:02:e3:55
08-22 16:38:25.039  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=5e:b4:71:02:e3:55
08-22 16:38:25.039  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=5e:b4:71:02:e3:55
08-22 16:38:25.039  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 5e:b4:71:02:e3:55
08-22 16:38:25.040  1593  1743 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 5e:b4:71:02:e3:55
08-22 16:38:25.041  1593  1743 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 5e:b4:71:02:e3:55
08-22 16:38:25.042  1593  1743 D BluetoothRemoteDevices: Added new device property
08-22 16:38:25.043  1593  1743 D BluetoothRemoteDevices: Removing device D0:49:7C:57:29:E6 from property map
08-22 16:38:25.043  1593  1743 D BluetoothRemoteDevices: Property type: 2
08-22 16:38:25.043  1593  1743 D BluetoothRemoteDevices: Remote Address is:5E:B4:71:02:E3:55
08-22 16:38:25.043  1593  1743 D BluetoothRemoteDevices: Property type: 5
08-22 16:38:25.043  1593  1743 D BluetoothRemoteDevices: BT_PROPERTY_TYPE_OF_DEVICE 5E:B4:71:02:E3:55
08-22 16:38:25.043  1593  1743 D BluetoothRemoteDevices: Property type: 11
08-22 16:38:25.043  1593  1743 D BluetoothRemoteDevices: deviceFoundCallback: Remote Address is:5E:B4:71:02:E3:55
08-22 16:38:25.045  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=22:68:f4:28:0f:21
08-22 16:38:25.045  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=22:68:f4:28:0f:21
08-22 16:38:25.045  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 22:68:f4:28:0f:21
08-22 16:38:25.048  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=4f:55:0d:58:a4:e1
08-22 16:38:25.048  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=4f:55:0d:58:a4:e1
08-22 16:38:25.048  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 4f:55:0d:58:a4:e1
08-22 16:38:25.048  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=4f:55:0d:58:a4:e1
08-22 16:38:25.048  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=4f:55:0d:58:a4:e1
08-22 16:38:25.048  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 4f:55:0d:58:a4:e1
08-22 16:38:25.050 15954 15954 D BluetoothDiscovery: Discovered unexpected device: Unknown Device, MAC: 5E:B4:71:02:E3:55, RSSI: -78 dBm
08-22 16:38:25.070  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=5e:ac:ee:80:be:93
08-22 16:38:25.070  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=5e:ac:ee:80:be:93
08-22 16:38:25.070  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 5e:ac:ee:80:be:93
08-22 16:38:25.070  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=63:a7:5c:98:77:90
08-22 16:38:25.070  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=63:a7:5c:98:77:90
08-22 16:38:25.070  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 63:a7:5c:98:77:90
08-22 16:38:25.070  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=63:a7:5c:98:77:90
08-22 16:38:25.070  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=63:a7:5c:98:77:90
08-22 16:38:25.070  1593  2395 W bt_btm_ble: btm_ble_process_adv_pkt_cont device no longer discoverable, discarding advertising packet
08-22 16:38:25.076  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:25.076  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:25.076  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:25.076  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:25.076  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:25.077  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:25.077  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=23:da:da:c8:2d:cf
08-22 16:38:25.077  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=23:da:da:c8:2d:cf
08-22 16:38:25.077  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 23:da:da:c8:2d:cf
08-22 16:38:25.087  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=ff:8b:e6:0b:72:a3
08-22 16:38:25.088  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=ff:8b:e6:0b:72:a3
08-22 16:38:25.088  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response ff:8b:e6:0b:72:a3
08-22 16:38:25.088  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=ff:8b:e6:0b:72:a3
08-22 16:38:25.088  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=ff:8b:e6:0b:72:a3
08-22 16:38:25.088  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA ff:8b:e6:0b:72:a3
08-22 16:38:25.091  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=06:ad:20:9e:b9:fd
08-22 16:38:25.091  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=06:ad:20:9e:b9:fd
08-22 16:38:25.091  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 06:ad:20:9e:b9:fd
08-22 16:38:25.131   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_SLEEP_IND: 0xFE
08-22 16:38:25.131   749  2393 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK OFF
08-22 16:38:25.281   749  2099 D vendor.qti.bluetooth@1.0-wake_lock: Release wakelock is released 
08-22 16:38:25.362 15954 15996 D valid   : parameter Ecg
08-22 16:38:25.362 15954 15996 D valid   : parameter BpSys
08-22 16:38:25.362 15954 15996 D valid   : parameter Fall
08-22 16:38:25.362 15954 15996 D valid   : parameter BpDia
08-22 16:38:25.362 15954 15996 D valid   : parameter Spo2
08-22 16:38:25.362 15954 15996 D valid   : parameter Resp
08-22 16:38:25.362 15954 15996 D valid   : parameter PR
08-22 16:38:25.362 15954 15996 D valid   : parameter Temp
08-22 16:38:25.362 15954 15996 D valid   : parameter ConnectivityAlarm
08-22 16:38:25.935 15954 15954 D DatabaseDebug: Passphrase: mykey
08-22 16:38:26.011 15954 16005 I OpenGLRenderer: Davey! duration=9223349055341ms; Flags=0, FrameTimelineVsyncId=576800, IntendedVsync=22981513203224, Vsync=22981596536554, InputEventId=0, HandleInputStart=22981603921648, AnimationStart=22981603928366, PerformTraversalsStart=22981603932741, DrawStart=22981605077116, FrameDeadline=22981529869890, FrameInterval=22981603899304, FrameStartTime=16666666, SyncQueued=22981606823939, SyncStart=22981607079096, IssueDrawCommandsStart=22981607276596, SwapBuffers=22981609359721, FrameCompleted=9223372036854775807, DequeueBufferDuration=25885, QueueBufferDuration=735730, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22981610620294, DisplayPresentTime=0, 
08-22 16:38:26.144  1609 15112 I OpenGLRenderer: Davey! duration=9223349055124ms; Flags=0, FrameTimelineVsyncId=576806, IntendedVsync=22981729778426, Vsync=22981729778426, InputEventId=0, HandleInputStart=22981730964043, AnimationStart=22981730968314, PerformTraversalsStart=22981730970502, DrawStart=22981735429356, FrameDeadline=22981746445092, FrameInterval=22981730954043, FrameStartTime=16666666, SyncQueued=22981735694668, SyncStart=22981735981023, IssueDrawCommandsStart=22981736188627, SwapBuffers=22981739132325, FrameCompleted=9223372036854775807, DequeueBufferDuration=48750, QueueBufferDuration=2086615, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22981742410398, DisplayPresentTime=0, 
08-22 16:38:26.365 15954 15996 D valid   : parameter Ecg
08-22 16:38:26.365 15954 15996 D valid   : parameter BpSys
08-22 16:38:26.365 15954 15996 D valid   : parameter Fall
08-22 16:38:26.365 15954 15996 D valid   : parameter BpDia
08-22 16:38:26.365 15954 15996 D valid   : parameter Spo2
08-22 16:38:26.365 15954 15996 D valid   : parameter Resp
08-22 16:38:26.365 15954 15996 D valid   : parameter PR
08-22 16:38:26.365 15954 15996 D valid   : parameter Temp
08-22 16:38:26.365 15954 15996 D valid   : parameter ConnectivityAlarm
08-22 16:38:26.421   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_IND: 0xFD
08-22 16:38:26.421   749  2393 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK ON
08-22 16:38:26.426   749  2393 D vendor.qti.bluetooth@1.0-wake_lock: Acquire wakelock is acquired 
08-22 16:38:26.426   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Writing IBS_WAKE_ACK
08-22 16:38:26.429  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=22:68:f4:28:0f:21
08-22 16:38:26.429  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=22:68:f4:28:0f:21
08-22 16:38:26.429  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 22:68:f4:28:0f:21
08-22 16:38:26.436  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:26.436  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:26.436  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:26.436  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:26.437  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:26.437  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:26.441  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=5e:ac:ee:80:be:93
08-22 16:38:26.441  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=5e:ac:ee:80:be:93
08-22 16:38:26.441  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 5e:ac:ee:80:be:93
08-22 16:38:26.441  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=5e:ac:ee:80:be:93
08-22 16:38:26.441  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=5e:ac:ee:80:be:93
08-22 16:38:26.441  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 5e:ac:ee:80:be:93
08-22 16:38:26.443  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=0e:7c:ce:be:dc:e7
08-22 16:38:26.443  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=0e:7c:ce:be:dc:e7
08-22 16:38:26.443  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 0e:7c:ce:be:dc:e7
08-22 16:38:26.446  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=23:da:da:c8:2d:cf
08-22 16:38:26.446  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=23:da:da:c8:2d:cf
08-22 16:38:26.446  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 23:da:da:c8:2d:cf
08-22 16:38:26.470  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=d6:33:1c:d1:03:f6
08-22 16:38:26.470  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=d6:33:1c:d1:03:f6
08-22 16:38:26.470  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response d6:33:1c:d1:03:f6
08-22 16:38:26.470  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=d6:33:1c:d1:03:f6
08-22 16:38:26.470  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=d6:33:1c:d1:03:f6
08-22 16:38:26.470  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA d6:33:1c:d1:03:f6
08-22 16:38:26.476  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:26.476  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:26.476  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:26.476  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:26.476  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:26.477  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:26.489  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=25:13:9d:26:84:51
08-22 16:38:26.490  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=25:13:9d:26:84:51
08-22 16:38:26.490  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 25:13:9d:26:84:51
08-22 16:38:26.506  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=52:b7:d9:95:37:dd
08-22 16:38:26.506  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=52:b7:d9:95:37:dd
08-22 16:38:26.506  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 52:b7:d9:95:37:dd
08-22 16:38:26.506  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=52:b7:d9:95:37:dd
08-22 16:38:26.506  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=52:b7:d9:95:37:dd
08-22 16:38:26.506  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 52:b7:d9:95:37:dd
08-22 16:38:26.509  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=22:68:f4:28:0f:21
08-22 16:38:26.509  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=22:68:f4:28:0f:21
08-22 16:38:26.510  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 22:68:f4:28:0f:21
08-22 16:38:26.549  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=23:da:da:c8:2d:cf
08-22 16:38:26.549  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=23:da:da:c8:2d:cf
08-22 16:38:26.549  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 23:da:da:c8:2d:cf
08-22 16:38:26.551  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=0e:7c:ce:be:dc:e7
08-22 16:38:26.551  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=0e:7c:ce:be:dc:e7
08-22 16:38:26.551  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 0e:7c:ce:be:dc:e7
08-22 16:38:26.557  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=06:ad:20:9e:b9:fd
08-22 16:38:26.557  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=06:ad:20:9e:b9:fd
08-22 16:38:26.557  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 06:ad:20:9e:b9:fd
08-22 16:38:26.559  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:26.560  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:26.560  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:26.560  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:26.560  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:26.560  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:26.591  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=25:13:9d:26:84:51
08-22 16:38:26.591  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=25:13:9d:26:84:51
08-22 16:38:26.591  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 25:13:9d:26:84:51
08-22 16:38:26.608  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:26.609  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:26.609  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:26.609  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:26.609  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:26.609  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:26.618  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=22:68:f4:28:0f:21
08-22 16:38:26.618  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=22:68:f4:28:0f:21
08-22 16:38:26.618  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 22:68:f4:28:0f:21
08-22 16:38:26.635  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=ff:8b:e6:0b:72:a3
08-22 16:38:26.635  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=ff:8b:e6:0b:72:a3
08-22 16:38:26.635  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response ff:8b:e6:0b:72:a3
08-22 16:38:26.635  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=ff:8b:e6:0b:72:a3
08-22 16:38:26.635  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=ff:8b:e6:0b:72:a3
08-22 16:38:26.635  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA ff:8b:e6:0b:72:a3
08-22 16:38:26.674   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_SLEEP_IND: 0xFE
08-22 16:38:26.674   749  2393 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK OFF
08-22 16:38:26.824   749  2099 D vendor.qti.bluetooth@1.0-wake_lock: Release wakelock is released 
08-22 16:38:27.033 15954 15954 D DatabaseDebug: Passphrase: mykey
08-22 16:38:27.111 15954 16057 I OpenGLRenderer: Davey! duration=9223349054240ms; Flags=0, FrameTimelineVsyncId=576835, IntendedVsync=22982614155012, Vsync=22982697488342, InputEventId=0, HandleInputStart=22982706453054, AnimationStart=22982706459095, PerformTraversalsStart=22982706467376, DrawStart=22982707564616, FrameDeadline=22982647488344, FrameInterval=22982706429460, FrameStartTime=16666666, SyncQueued=22982709696231, SyncStart=22982709918522, IssueDrawCommandsStart=22982710182845, SwapBuffers=22982713033939, FrameCompleted=9223372036854775807, DequeueBufferDuration=25990, QueueBufferDuration=662396, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22982714103939, DisplayPresentTime=0, 
08-22 16:38:27.145 15954 16057 I OpenGLRenderer: Davey! duration=9223349054123ms; Flags=0, FrameTimelineVsyncId=576838, IntendedVsync=22982730931364, Vsync=22982730931364, InputEventId=0, HandleInputStart=22982732827793, AnimationStart=22982732840033, PerformTraversalsStart=22982732845553, DrawStart=22982735129876, FrameDeadline=22982764264696, FrameInterval=22982732799668, FrameStartTime=16666666, SyncQueued=22982736314772, SyncStart=22982736637793, IssueDrawCommandsStart=22982736923262, SwapBuffers=22982740807064, FrameCompleted=9223372036854775807, DequeueBufferDuration=45990, QueueBufferDuration=776719, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22982742877689, DisplayPresentTime=0, 
08-22 16:38:27.367 15954 15996 D valid   : parameter Ecg
08-22 16:38:27.367 15954 15996 D valid   : parameter BpSys
08-22 16:38:27.368 15954 15996 D valid   : parameter Fall
08-22 16:38:27.368 15954 15996 D valid   : parameter BpDia
08-22 16:38:27.368 15954 15996 D valid   : parameter Spo2
08-22 16:38:27.368 15954 15996 D valid   : parameter Resp
08-22 16:38:27.368 15954 15996 D valid   : parameter PR
08-22 16:38:27.368 15954 15996 D valid   : parameter Temp
08-22 16:38:27.368 15954 15996 D valid   : parameter ConnectivityAlarm
08-22 16:38:27.969   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_IND: 0xFD
08-22 16:38:27.970   749  2393 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK ON
08-22 16:38:27.975   749  2393 D vendor.qti.bluetooth@1.0-wake_lock: Acquire wakelock is acquired 
08-22 16:38:27.975   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Writing IBS_WAKE_ACK
08-22 16:38:27.978  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=66:05:af:71:23:55
08-22 16:38:27.979  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=66:05:af:71:23:55
08-22 16:38:27.979  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 66:05:af:71:23:55
08-22 16:38:27.979  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=b0:10:a0:f5:e3:eb
08-22 16:38:27.979  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=b0:10:a0:f5:e3:eb
08-22 16:38:27.979  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response b0:10:a0:f5:e3:eb
08-22 16:38:27.979  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=22:68:f4:28:0f:21
08-22 16:38:27.979  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=22:68:f4:28:0f:21
08-22 16:38:27.979  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 22:68:f4:28:0f:21
08-22 16:38:27.979  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:27.979  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:27.979  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:27.979  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:27.979  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:27.979  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:27.985  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=71:60:bf:cf:31:8b
08-22 16:38:27.985  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=71:60:bf:cf:31:8b
08-22 16:38:27.985  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 71:60:bf:cf:31:8b
08-22 16:38:28.012  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=d6:33:1c:d1:03:f6
08-22 16:38:28.012  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=d6:33:1c:d1:03:f6
08-22 16:38:28.012  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response d6:33:1c:d1:03:f6
08-22 16:38:28.013  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=23:da:da:c8:2d:cf
08-22 16:38:28.013  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=23:da:da:c8:2d:cf
08-22 16:38:28.013  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 23:da:da:c8:2d:cf
08-22 16:38:28.015  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:28.015  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:28.015  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:28.016  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:28.016  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:28.016  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:28.020  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=0e:7c:ce:be:dc:e7
08-22 16:38:28.020  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=0e:7c:ce:be:dc:e7
08-22 16:38:28.020  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 0e:7c:ce:be:dc:e7
08-22 16:38:28.027  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=06:ad:20:9e:b9:fd
08-22 16:38:28.027  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=06:ad:20:9e:b9:fd
08-22 16:38:28.027  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 06:ad:20:9e:b9:fd
08-22 16:38:28.033  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=4f:55:0d:58:a4:e1
08-22 16:38:28.033  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=4f:55:0d:58:a4:e1
08-22 16:38:28.033  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 4f:55:0d:58:a4:e1
08-22 16:38:28.033  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=4f:55:0d:58:a4:e1
08-22 16:38:28.033  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=4f:55:0d:58:a4:e1
08-22 16:38:28.033  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 4f:55:0d:58:a4:e1
08-22 16:38:28.037  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=25:13:9d:26:84:51
08-22 16:38:28.037  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=25:13:9d:26:84:51
08-22 16:38:28.037  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 25:13:9d:26:84:51
08-22 16:38:28.043  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=5d:e1:1e:ff:f5:ab
08-22 16:38:28.043  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=5d:e1:1e:ff:f5:ab
08-22 16:38:28.043  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 5d:e1:1e:ff:f5:ab
08-22 16:38:28.043  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=5d:e1:1e:ff:f5:ab
08-22 16:38:28.043  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=5d:e1:1e:ff:f5:ab
08-22 16:38:28.043  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 5d:e1:1e:ff:f5:ab
08-22 16:38:28.055  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=94:32:51:02:2c:19
08-22 16:38:28.055  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=94:32:51:02:2c:19
08-22 16:38:28.055  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 94:32:51:02:2c:19
08-22 16:38:28.055  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=5e:b4:71:02:e3:55
08-22 16:38:28.055  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=5e:b4:71:02:e3:55
08-22 16:38:28.055  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 5e:b4:71:02:e3:55
08-22 16:38:28.055  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=5e:b4:71:02:e3:55
08-22 16:38:28.055  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=5e:b4:71:02:e3:55
08-22 16:38:28.055  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 5e:b4:71:02:e3:55
08-22 16:38:28.062  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=52:b7:d9:95:37:dd
08-22 16:38:28.063  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=52:b7:d9:95:37:dd
08-22 16:38:28.063  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 52:b7:d9:95:37:dd
08-22 16:38:28.063  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=22:68:f4:28:0f:21
08-22 16:38:28.063  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=22:68:f4:28:0f:21
08-22 16:38:28.063  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 22:68:f4:28:0f:21
08-22 16:38:28.081  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=5e:ac:ee:80:be:93
08-22 16:38:28.081  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=5e:ac:ee:80:be:93
08-22 16:38:28.081  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 5e:ac:ee:80:be:93
08-22 16:38:28.081  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=5e:ac:ee:80:be:93
08-22 16:38:28.081  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=5e:ac:ee:80:be:93
08-22 16:38:28.081  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 5e:ac:ee:80:be:93
08-22 16:38:28.106  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=60:a3:69:bf:ab:ed
08-22 16:38:28.106  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=60:a3:69:bf:ab:ed
08-22 16:38:28.106  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 60:a3:69:bf:ab:ed
08-22 16:38:28.107  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:28.107  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:28.107  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:28.108  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:28.108  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:28.108  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:28.116  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=23:da:da:c8:2d:cf
08-22 16:38:28.117  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=23:da:da:c8:2d:cf
08-22 16:38:28.117  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 23:da:da:c8:2d:cf
08-22 16:38:28.119  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=0e:7c:ce:be:dc:e7
08-22 16:38:28.119  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=0e:7c:ce:be:dc:e7
08-22 16:38:28.119  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 0e:7c:ce:be:dc:e7
08-22 16:38:28.129  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=fa:a3:1f:14:dd:ef
08-22 16:38:28.130  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=fa:a3:1f:14:dd:ef
08-22 16:38:28.130  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response fa:a3:1f:14:dd:ef
08-22 16:38:28.130  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=06:ad:20:9e:b9:fd
08-22 16:38:28.130  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=06:ad:20:9e:b9:fd
08-22 16:38:28.130  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 06:ad:20:9e:b9:fd
08-22 16:38:28.135 15954 15954 D DatabaseDebug: Passphrase: mykey
08-22 16:38:28.137  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=60:a3:69:bf:ab:ed
08-22 16:38:28.137  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=60:a3:69:bf:ab:ed
08-22 16:38:28.137  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 60:a3:69:bf:ab:ed
08-22 16:38:28.137  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=60:a3:69:bf:ab:ed
08-22 16:38:28.138  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=60:a3:69:bf:ab:ed
08-22 16:38:28.138  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 60:a3:69:bf:ab:ed
08-22 16:38:28.138  1593  1743 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 60:a3:69:bf:ab:ed
08-22 16:38:28.140  1593  1743 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 60:a3:69:bf:ab:ed
08-22 16:38:28.141  1593  1743 D BluetoothRemoteDevices: Added new device property
08-22 16:38:28.142  1593  1743 D BluetoothRemoteDevices: Removing device 08:B9:25:A8:ED:A3 from property map
08-22 16:38:28.142  1593  1743 D BluetoothRemoteDevices: Property type: 2
08-22 16:38:28.142  1593  1743 D BluetoothRemoteDevices: Remote Address is:60:A3:69:BF:AB:ED
08-22 16:38:28.142  1593  1743 D BluetoothRemoteDevices: Property type: 1
08-22 16:38:28.143  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=25:13:9d:26:84:51
08-22 16:38:28.143  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=25:13:9d:26:84:51
08-22 16:38:28.143  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 25:13:9d:26:84:51
08-22 16:38:28.144  1593  1743 I BluetoothAdapterService:  isIgnoreDevice false device name null addr 60:A3:69:BF:AB:ED
08-22 16:38:28.146  1593  1743 D BluetoothRemoteDevices: Remote Device name is: TVSNT0CLB2530
08-22 16:38:28.146  1593  1743 D BluetoothRemoteDevices: Property type: 5
08-22 16:38:28.146  1593  1743 D BluetoothRemoteDevices: BT_PROPERTY_TYPE_OF_DEVICE 60:A3:69:BF:AB:ED
08-22 16:38:28.146  1593  1743 D BluetoothRemoteDevices: Property type: 11
08-22 16:38:28.146  1593  1743 D BluetoothRemoteDevices: deviceFoundCallback: Remote Address is:60:A3:69:BF:AB:ED
08-22 16:38:28.174  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=60:a3:69:bf:ab:ed
08-22 16:38:28.174  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=60:a3:69:bf:ab:ed
08-22 16:38:28.174  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 60:a3:69:bf:ab:ed
08-22 16:38:28.174  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=60:a3:69:bf:ab:ed
08-22 16:38:28.174  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=60:a3:69:bf:ab:ed
08-22 16:38:28.174  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 60:a3:69:bf:ab:ed
08-22 16:38:28.182  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=63:a7:5c:98:77:90
08-22 16:38:28.182  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=63:a7:5c:98:77:90
08-22 16:38:28.182  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 63:a7:5c:98:77:90
08-22 16:38:28.182  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=63:a7:5c:98:77:90
08-22 16:38:28.182  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=63:a7:5c:98:77:90
08-22 16:38:28.182  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 63:a7:5c:98:77:90
08-22 16:38:28.197  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=e5:53:7c:25:b6:37
08-22 16:38:28.197  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=e5:53:7c:25:b6:37
08-22 16:38:28.197  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response e5:53:7c:25:b6:37
08-22 16:38:28.198  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=4f:55:0d:58:a4:e1
08-22 16:38:28.198  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=4f:55:0d:58:a4:e1
08-22 16:38:28.198  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 4f:55:0d:58:a4:e1
08-22 16:38:28.198  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=4f:55:0d:58:a4:e1
08-22 16:38:28.199  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=4f:55:0d:58:a4:e1
08-22 16:38:28.199  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 4f:55:0d:58:a4:e1
08-22 16:38:28.210 15954 16057 I OpenGLRenderer: Davey! duration=9223349053141ms; Flags=0, FrameTimelineVsyncId=576841, IntendedVsync=22983713359681, Vsync=22983796693011, InputEventId=0, HandleInputStart=22983804804251, AnimationStart=22983804814303, PerformTraversalsStart=22983804822376, DrawStart=22983806564147, FrameDeadline=22983746693013, FrameInterval=22983804777116, FrameStartTime=16666666, SyncQueued=22983808355970, SyncStart=22983808571022, IssueDrawCommandsStart=22983808757480, SwapBuffers=22983810966699, FrameCompleted=9223372036854775807, DequeueBufferDuration=25104, QueueBufferDuration=741718, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22983812313053, DisplayPresentTime=0, 
08-22 16:38:28.214 15954 15954 D BluetoothDiscovery: Discovered unexpected device: TVSNT0CLB2530, MAC: 60:A3:69:BF:AB:ED, RSSI: -91 dBm
08-22 16:38:28.216  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=52:b7:d9:95:37:dd
08-22 16:38:28.216  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=52:b7:d9:95:37:dd
08-22 16:38:28.216  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response 52:b7:d9:95:37:dd
08-22 16:38:28.216  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=52:b7:d9:95:37:dd
08-22 16:38:28.216  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=52:b7:d9:95:37:dd
08-22 16:38:28.216  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 52:b7:d9:95:37:dd
08-22 16:38:28.220  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=23:da:da:c8:2d:cf
08-22 16:38:28.220  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=23:da:da:c8:2d:cf
08-22 16:38:28.220  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 23:da:da:c8:2d:cf
08-22 16:38:28.225  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=0e:7c:ce:be:dc:e7
08-22 16:38:28.225  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=0e:7c:ce:be:dc:e7
08-22 16:38:28.225  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 0e:7c:ce:be:dc:e7
08-22 16:38:28.233  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=e2:0f:a6:c9:13:06
08-22 16:38:28.233  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=e2:0f:a6:c9:13:06
08-22 16:38:28.233  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response e2:0f:a6:c9:13:06
08-22 16:38:28.233  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=e2:0f:a6:c9:13:06
08-22 16:38:28.233  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=e2:0f:a6:c9:13:06
08-22 16:38:28.233  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA e2:0f:a6:c9:13:06
08-22 16:38:28.233  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=06:ad:20:9e:b9:fd
08-22 16:38:28.234  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=06:ad:20:9e:b9:fd
08-22 16:38:28.234  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 06:ad:20:9e:b9:fd
08-22 16:38:28.242  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:28.242  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:28.242  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2855)]  Waiting for scan response f0:9e:9e:0f:e9:25
08-22 16:38:28.242  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=f0:9e:9e:0f:e9:25
08-22 16:38:28.242  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=f0:9e:9e:0f:e9:25
08-22 16:38:28.242  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA f0:9e:9e:0f:e9:25
08-22 16:38:28.245  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2726)] btm_ble_process_ext_adv_pkt: bda=25:13:9d:26:84:51
08-22 16:38:28.245  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2650)] btm_ble_process_adv_addr: bda=25:13:9d:26:84:51
08-22 16:38:28.245  1593  2395 V bt_stack: [VERBOSE1:btm_ble_gap.cc(2886)] btm_ble_process_adv_pkt_cont skipped BDA 25:13:9d:26:84:51
08-22 16:38:28.285   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_SLEEP_IND: 0xFE
08-22 16:38:28.285   749  2393 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK OFF
08-22 16:38:28.371 15954 15996 D valid   : parameter Ecg
08-22 16:38:28.371 15954 15996 D valid   : parameter BpSys
08-22 16:38:28.371 15954 15996 D valid   : parameter Fall
08-22 16:38:28.371 15954 15996 D valid   : parameter BpDia
08-22 16:38:28.371 15954 15996 D valid   : parameter Spo2
08-22 16:38:28.371 15954 15996 D valid   : parameter Resp
08-22 16:38:28.371 15954 15996 D valid   : parameter PR
08-22 16:38:28.371 15954 15996 D valid   : parameter Temp
08-22 16:38:28.371 15954 15996 D valid   : parameter ConnectivityAlarm
08-22 16:38:28.435   749  2099 D vendor.qti.bluetooth@1.0-wake_lock: Release wakelock is released 
08-22 16:38:28.452   749   749 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK ON
08-22 16:38:28.456   749   749 D vendor.qti.bluetooth@1.0-wake_lock: Acquire wakelock is acquired 
08-22 16:38:28.456   749   749 I vendor.qti.bluetooth@1.0-ibs_handler: DeviceWakeUp: Writing IBS_WAKE_IND
08-22 16:38:28.457   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_ACK: 0xFC
08-22 16:38:28.457   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Signal wack_cond_
08-22 16:38:28.457   749   749 D vendor.qti.bluetooth@1.0-ibs_handler: DeviceWakeUp: Unblocked from waiting for FC, pthread_cond_timedwait ret = 0
08-22 16:38:28.459   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_IND: 0xFD
08-22 16:38:28.459   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Writing IBS_WAKE_ACK
08-22 16:38:28.503   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_SLEEP_IND: 0xFE
08-22 16:38:29.232 15954 15954 D DatabaseDebug: Passphrase: mykey
08-22 16:38:29.310 15954 16057 I OpenGLRenderer: Davey! duration=9223349052041ms; Flags=0, FrameTimelineVsyncId=576847, IntendedVsync=22984813224212, Vsync=22984896557542, InputEventId=0, HandleInputStart=22984898412792, AnimationStart=22984898419615, PerformTraversalsStart=22984898424407, DrawStart=22984899528313, FrameDeadline=22984846557544, FrameInterval=22984898399146, FrameStartTime=16666666, SyncQueued=22984901898521, SyncStart=22984902341438, IssueDrawCommandsStart=22984902745396, SwapBuffers=22984904986542, FrameCompleted=9223372036854775807, DequeueBufferDuration=23698, QueueBufferDuration=851562, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22984906236021, DisplayPresentTime=0, 
08-22 16:38:29.328 15954 16057 I OpenGLRenderer: Davey! duration=9223349051941ms; Flags=0, FrameTimelineVsyncId=576850, IntendedVsync=22984913029608, Vsync=22984913029608, InputEventId=0, HandleInputStart=22984918120553, AnimationStart=22984918128417, PerformTraversalsStart=22984918133834, DrawStart=22984919861803, FrameDeadline=22984946362940, FrameInterval=22984918103626, FrameStartTime=16666666, SyncQueued=22984920799928, SyncStart=22984921263990, IssueDrawCommandsStart=22984921550292, SwapBuffers=22984923939615, FrameCompleted=9223372036854775807, DequeueBufferDuration=23386, QueueBufferDuration=619375, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22984924986542, DisplayPresentTime=0, 
08-22 16:38:29.373 15954 15996 D valid   : parameter Ecg
08-22 16:38:29.374 15954 15996 D valid   : parameter BpSys
08-22 16:38:29.374 15954 15996 D valid   : parameter Fall
08-22 16:38:29.374 15954 15996 D valid   : parameter BpDia
08-22 16:38:29.374 15954 15996 D valid   : parameter Spo2
08-22 16:38:29.374 15954 15996 D valid   : parameter Resp
08-22 16:38:29.374 15954 15996 D valid   : parameter PR
08-22 16:38:29.374 15954 15996 D valid   : parameter Temp
08-22 16:38:29.374 15954 15996 D valid   : parameter ConnectivityAlarm
08-22 16:38:29.462   749  2398 I vendor.qti.bluetooth@1.0-ibs_handler: DeviceSleep: TX Awake, Sending SLEEP_IND
08-22 16:38:29.462   749  2398 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK OFF
08-22 16:38:29.612   749  2099 D vendor.qti.bluetooth@1.0-wake_lock: Release wakelock is released 
08-22 16:38:30.324 15954 15954 D DatabaseDebug: Passphrase: mykey
08-22 16:38:30.376 15954 15996 D valid   : parameter Ecg
08-22 16:38:30.376 15954 15996 D valid   : parameter BpSys
08-22 16:38:30.376 15954 15996 D valid   : parameter Fall
08-22 16:38:30.376 15954 15996 D valid   : parameter BpDia
08-22 16:38:30.376 15954 15996 D valid   : parameter Spo2
08-22 16:38:30.377 15954 15996 D valid   : parameter Resp
08-22 16:38:30.380 15954 15996 D valid   : parameter PR
08-22 16:38:30.380 15954 15996 D valid   : parameter Temp
08-22 16:38:30.380 15954 15996 D valid   : parameter ConnectivityAlarm
08-22 16:38:31.382 15954 15993 D valid   : parameter Ecg
08-22 16:38:31.383 15954 15993 D valid   : parameter BpSys
08-22 16:38:31.383 15954 15993 D valid   : parameter Fall
08-22 16:38:31.383 15954 15993 D valid   : parameter BpDia
08-22 16:38:31.383 15954 15993 D valid   : parameter Spo2
08-22 16:38:31.383 15954 15993 D valid   : parameter Resp
08-22 16:38:31.383 15954 15993 D valid   : parameter PR
08-22 16:38:31.383 15954 15993 D valid   : parameter Temp
08-22 16:38:31.383 15954 15993 D valid   : parameter ConnectivityAlarm
08-22 16:38:31.418 15954 15954 D DatabaseDebug: Passphrase: mykey
08-22 16:38:31.567   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_IND: 0xFD
08-22 16:38:31.568   749  2393 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK ON
08-22 16:38:31.572   749  2393 D vendor.qti.bluetooth@1.0-wake_lock: Acquire wakelock is acquired 
08-22 16:38:31.572   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Writing IBS_WAKE_ACK
08-22 16:38:31.575  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr f0:9e:9e:0f:e9:25
08-22 16:38:31.575  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr ff:8b:e6:0b:72:a3
08-22 16:38:31.575  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr e5:53:7c:25:b6:37
08-22 16:38:31.575  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr e2:0f:a6:c9:13:06
08-22 16:38:31.575  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 25:13:9d:26:84:51
08-22 16:38:31.575   749   749 I vendor.qti.bluetooth@1.0-ibs_handler: DeviceWakeUp: Writing IBS_WAKE_IND
08-22 16:38:31.575  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 0e:7c:ce:be:dc:e7
08-22 16:38:31.575  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 22:68:f4:28:0f:21
08-22 16:38:31.575  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 5e:ac:ee:80:be:93
08-22 16:38:31.575  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr c7:1c:d9:28:26:34
08-22 16:38:31.575  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr e2:50:9f:15:8f:9c
08-22 16:38:31.575  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr d8:b2:4d:09:ff:07
08-22 16:38:31.575  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 18:d6:5b:46:5a:5c
08-22 16:38:31.576  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 23:da:da:c8:2d:cf
08-22 16:38:31.576  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 52:b7:d9:95:37:dd
08-22 16:38:31.576  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 63:a7:5c:98:77:90
08-22 16:38:31.576  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr d6:33:1c:d1:03:f6
08-22 16:38:31.576  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 5e:b4:71:02:e3:55
08-22 16:38:31.576  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr f2:03:30:3e:10:74
08-22 16:38:31.576  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 30:e7:bc:de:fa:ae
08-22 16:38:31.576  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(960)] BTM_ReadRemoteDeviceName: bd addr 30:e7:bc:de:fa:ae
08-22 16:38:31.576  1593  2395 V bt_stack: [VERBOSE1:btm_acl.cc(3014)] connecting_bda: f0:67:28:40:7c:98
08-22 16:38:31.576   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_ACK: 0xFC
08-22 16:38:31.576   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Signal wack_cond_
08-22 16:38:31.576  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 30:e7:bc:de:fa:ae
08-22 16:38:31.576   749   749 D vendor.qti.bluetooth@1.0-ibs_handler: DeviceWakeUp: Unblocked from waiting for FC, pthread_cond_timedwait ret = 0
08-22 16:38:31.620   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_SLEEP_IND: 0xFE
08-22 16:38:32.386 15954 15993 D valid   : parameter Ecg
08-22 16:38:32.386 15954 15993 D valid   : parameter BpSys
08-22 16:38:32.386 15954 15993 D valid   : parameter Fall
08-22 16:38:32.386 15954 15993 D valid   : parameter BpDia
08-22 16:38:32.386 15954 15993 D valid   : parameter Spo2
08-22 16:38:32.386 15954 15993 D valid   : parameter Resp
08-22 16:38:32.386 15954 15993 D valid   : parameter PR
08-22 16:38:32.386 15954 15993 D valid   : parameter Temp
08-22 16:38:32.386 15954 15993 D valid   : parameter ConnectivityAlarm
08-22 16:38:32.521 15954 15954 D DatabaseDebug: Passphrase: mykey
08-22 16:38:32.578   749  2398 I vendor.qti.bluetooth@1.0-ibs_handler: DeviceSleep: TX Awake, Sending SLEEP_IND
08-22 16:38:32.578   749  2398 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK OFF
08-22 16:38:32.622 15954 15954 D CompatibilityChangeReporter: Compat change id reported: 147798919; UID 10223; state: ENABLED
08-22 16:38:32.624   746  3209 D audio_hw_primary: start_output_stream: enter: stream(0xf2944330)usecase(1: low-latency-playback) devices(0x2) is_haptic_usecase(0)
08-22 16:38:32.624   746  3209 D audio_hw_primary: select_devices for use case (low-latency-playback)
08-22 16:38:32.624   746  3209 I msm8974_platform: platform_check_and_set_codec_backend_cfg:becf: afe: bitwidth 16, samplerate 48000 channels 2, backend_idx 0 usecase = 1 device (speaker)
08-22 16:38:32.624   746  3209 I msm8974_platform: platform_check_and_set_codec_backend_cfg: new_snd_devices[0] is 2
08-22 16:38:32.624   746  3209 I msm8974_platform: platform_check_codec_backend_cfg:becf: afe: bitwidth 16, samplerate 48000 channels 2, backend_idx 0 usecase = 1 device (speaker)
08-22 16:38:32.626 15954 16057 I OpenGLRenderer: Davey! duration=9223349048639ms; Flags=0, FrameTimelineVsyncId=576868, IntendedVsync=22988212885976, Vsync=22988212885976, InputEventId=725647440, HandleInputStart=22988217875291, AnimationStart=22988217887687, PerformTraversalsStart=22988217893156, DrawStart=22988221537843, FrameDeadline=22988229552642, FrameInterval=22988217844926, FrameStartTime=16666666, SyncQueued=22988221685499, SyncStart=22988224434562, IssueDrawCommandsStart=22988224535343, SwapBuffers=22988225990031, FrameCompleted=9223372036854775807, DequeueBufferDuration=20833, QueueBufferDuration=428438, GpuCompleted=9223372036854775807, SwapBuffersCompleted=**************, DisplayPresentTime=0, 
08-22 16:38:32.627   746  3209 D msm8974_platform: platform_check_codec_backend_cfg:becf: updated afe: bitwidth 16, samplerate 48000 channels 2,backend_idx 0 usecase = 1 device (speaker)
08-22 16:38:32.627   746  3209 I msm8974_platform: platform_check_codec_backend_cfg:becf: afe: Codec selected backend: 0 updated bit width: 16 and sample rate: 48000
08-22 16:38:32.627   746  3209 D audio_hw_primary: check_usecases_codec_backend:becf: force routing 0
08-22 16:38:32.627   746  3209 D audio_hw_primary: check_usecases_codec_backend:becf: (85) check_usecases curr device: speaker, usecase device: backends match 0
08-22 16:38:32.627   746  3209 D audio_hw_primary: check_usecases_codec_backend:becf: check_usecases num.of Usecases to switch 0
08-22 16:38:32.627   746  3209 D hardware_info: hw_info_append_hw_type : device_name = speaker
08-22 16:38:32.627   746  3209 D audio_hw_primary: enable_snd_device: snd_device(2: speaker)
08-22 16:38:32.627   746  3209 D msm8974_platform: platform_get_island_cfg_on_device:island cfg status on snd_device = (speaker 0)
08-22 16:38:32.627   746  3209 I soundtrigger: audio_extn_sound_trigger_update_device_status: device 0x2 of type 0 for Event 1, with Raise=0
08-22 16:38:32.627   746  3209 D audio_route: Apply path: speaker
08-22 16:38:32.628 15954 15954 D connect : Connecting to BMPMS 206
08-22 16:38:32.631  1609  1609 W ziparchive: Unable to open '/data/app/~~oX6TnzRMBs-_0OuSx-X8Dw==/com.bodymount.app-3xPyALhwOPYZ63ZoMEfaBg==/base.apk': No such file or directory
08-22 16:38:32.631  1609  1609 E ndroid.systemu: Failed to open APK '/data/app/~~oX6TnzRMBs-_0OuSx-X8Dw==/com.bodymount.app-3xPyALhwOPYZ63ZoMEfaBg==/base.apk': I/O error
08-22 16:38:32.632  1609  1609 W ResourcesManager: failed to preload asset path '/data/app/~~oX6TnzRMBs-_0OuSx-X8Dw==/com.bodymount.app-3xPyALhwOPYZ63ZoMEfaBg==/base.apk'
08-22 16:38:32.632  1609  1609 W ResourcesManager: java.io.IOException: Failed to load asset path /data/app/~~oX6TnzRMBs-_0OuSx-X8Dw==/com.bodymount.app-3xPyALhwOPYZ63ZoMEfaBg==/base.apk
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at android.content.res.ApkAssets.nativeLoad(Native Method)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at android.content.res.ApkAssets.<init>(ApkAssets.java:295)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at android.content.res.ApkAssets.loadFromPath(ApkAssets.java:144)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at android.app.ResourcesManager.loadApkAssets(ResourcesManager.java:454)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at android.app.ResourcesManager.access$000(ResourcesManager.java:72)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at android.app.ResourcesManager$ApkAssetsSupplier.load(ResourcesManager.java:168)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at android.app.ResourcesManager.createApkAssetsSupplierNotLocked(ResourcesManager.java:980)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at android.app.ResourcesManager.getResources(ResourcesManager.java:1101)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at android.app.ActivityThread.getTopLevelResources(ActivityThread.java:2416)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at android.app.ApplicationPackageManager.getResourcesForApplication(ApplicationPackageManager.java:1751)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at android.app.ApplicationPackageManager.getResourcesForApplication(ApplicationPackageManager.java:1737)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at android.app.ApplicationPackageManager.getDrawable(ApplicationPackageManager.java:1506)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at android.app.ApplicationPackageManager.loadUnbadgedItemIcon(ApplicationPackageManager.java:3029)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at android.content.pm.PackageItemInfo.loadUnbadgedIcon(PackageItemInfo.java:290)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at com.android.systemui.toast.SystemUIToast.getBadgedIcon(SystemUIToast.java:286)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at com.android.systemui.toast.SystemUIToast.inflateToastView(SystemUIToast.java:198)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at com.android.systemui.toast.SystemUIToast.<init>(SystemUIToast.java:90)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at com.android.systemui.toast.SystemUIToast.<init>(SystemUIToast.java:77)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at com.android.systemui.toast.ToastFactory.createToast(ToastFactory.java:78)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at com.android.systemui.toast.ToastUI.lambda$showToast$0(ToastUI.java:113)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at com.android.systemui.toast.ToastUI.$r8$lambda$w_gPCh3F8Xxn1jN4lkQZoUci71c(Unknown Source:0)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at com.android.systemui.toast.ToastUI$$ExternalSyntheticLambda0.run(Unknown Source:16)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at com.android.systemui.toast.ToastUI.showToast(ToastUI.java:140)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at com.android.systemui.statusbar.CommandQueue$H.handleMessage(CommandQueue.java:1458)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at android.os.Handler.dispatchMessage(Handler.java:106)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at android.os.Looper.loopOnce(Looper.java:201)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at android.os.Looper.loop(Looper.java:288)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at android.app.ActivityThread.main(ActivityThread.java:7911)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at java.lang.reflect.Method.invoke(Native Method)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
08-22 16:38:32.632  1609  1609 W ResourcesManager: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1009)
08-22 16:38:32.632  1609  1609 W ziparchive: Unable to open '/data/app/~~oX6TnzRMBs-_0OuSx-X8Dw==/com.bodymount.app-3xPyALhwOPYZ63ZoMEfaBg==/base.apk': No such file or directory
08-22 16:38:32.633  1609  1609 E ndroid.systemu: Failed to open APK '/data/app/~~oX6TnzRMBs-_0OuSx-X8Dw==/com.bodymount.app-3xPyALhwOPYZ63ZoMEfaBg==/base.apk': I/O error
08-22 16:38:32.634  1609  1609 E ResourcesManager: failed to add asset path '/data/app/~~oX6TnzRMBs-_0OuSx-X8Dw==/com.bodymount.app-3xPyALhwOPYZ63ZoMEfaBg==/base.apk'
08-22 16:38:32.634  1609  1609 E ResourcesManager: java.io.IOException: Failed to load asset path /data/app/~~oX6TnzRMBs-_0OuSx-X8Dw==/com.bodymount.app-3xPyALhwOPYZ63ZoMEfaBg==/base.apk
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at android.content.res.ApkAssets.nativeLoad(Native Method)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at android.content.res.ApkAssets.<init>(ApkAssets.java:295)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at android.content.res.ApkAssets.loadFromPath(ApkAssets.java:144)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at android.app.ResourcesManager.loadApkAssets(ResourcesManager.java:454)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at android.app.ResourcesManager.access$000(ResourcesManager.java:72)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at android.app.ResourcesManager$ApkAssetsSupplier.load(ResourcesManager.java:168)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at android.app.ResourcesManager.createAssetManager(ResourcesManager.java:530)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at android.app.ResourcesManager.createResourcesImpl(ResourcesManager.java:612)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at android.app.ResourcesManager.findOrCreateResourcesImplForKeyLocked(ResourcesManager.java:664)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at android.app.ResourcesManager.createResources(ResourcesManager.java:1011)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at android.app.ResourcesManager.getResources(ResourcesManager.java:1114)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at android.app.ActivityThread.getTopLevelResources(ActivityThread.java:2416)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at android.app.ApplicationPackageManager.getResourcesForApplication(ApplicationPackageManager.java:1751)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at android.app.ApplicationPackageManager.getResourcesForApplication(ApplicationPackageManager.java:1737)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at android.app.ApplicationPackageManager.getDrawable(ApplicationPackageManager.java:1506)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at android.app.ApplicationPackageManager.loadUnbadgedItemIcon(ApplicationPackageManager.java:3029)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at android.content.pm.PackageItemInfo.loadUnbadgedIcon(PackageItemInfo.java:290)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at com.android.systemui.toast.SystemUIToast.getBadgedIcon(SystemUIToast.java:286)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at com.android.systemui.toast.SystemUIToast.inflateToastView(SystemUIToast.java:198)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at com.android.systemui.toast.SystemUIToast.<init>(SystemUIToast.java:90)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at com.android.systemui.toast.SystemUIToast.<init>(SystemUIToast.java:77)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at com.android.systemui.toast.ToastFactory.createToast(ToastFactory.java:78)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at com.android.systemui.toast.ToastUI.lambda$showToast$0(ToastUI.java:113)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at com.android.systemui.toast.ToastUI.$r8$lambda$w_gPCh3F8Xxn1jN4lkQZoUci71c(Unknown Source:0)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at com.android.systemui.toast.ToastUI$$ExternalSyntheticLambda0.run(Unknown Source:16)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at com.android.systemui.toast.ToastUI.showToast(ToastUI.java:140)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at com.android.systemui.statusbar.CommandQueue$H.handleMessage(CommandQueue.java:1458)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at android.os.Handler.dispatchMessage(Handler.java:106)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at android.os.Looper.loopOnce(Looper.java:201)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at android.os.Looper.loop(Looper.java:288)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at android.app.ActivityThread.main(ActivityThread.java:7911)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at java.lang.reflect.Method.invoke(Native Method)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
08-22 16:38:32.634  1609  1609 E ResourcesManager: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1009)
08-22 16:38:32.634  1609  1609 W PackageManager: Failure retrieving resources for com.bodymount.app
08-22 16:38:32.639   746  3209 D audio_hw_primary: audio_is_true_native_stream_active:napb: (0) (low-latency-playback)id (1) sr 48000 bw (16) device speaker
08-22 16:38:32.639   746  3209 D soundtrigger: audio_extn_sound_trigger_update_stream_status: uc_info->id 1 of type 0 for Event 3, with Raise=0
08-22 16:38:32.639   746  3209 D audio_hw_utils: audio_extn_utils_send_app_type_cfg: usecase->out_snd_device speaker
08-22 16:38:32.640   746  3209 I audio_hw_utils: send_app_type_cfg_for_device PLAYBACK app_type 69937, acdb_dev_id 15, sample_rate 48000, snd_device_be_idx 170
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> send_audio_cal, acdb_id = 15, path = 0, app id = 0x11131, sample rate = 48000, use_case = 0,buffer_idx_w_path =0, afe_sample_rate = 48000, cal_mode = 1, offset_index = 0
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> send_asm_topology
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_STREAM_TOPOLOGY_ID
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> send_adm_topology
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_COMMON_TOPOLOGY_ID
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> send_audtable
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_COMMON_TABLE_SIZE
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_COMMON_TABLE
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> AUDIO_SET_AUDPROC_CAL cal_type[11] acdb_id[15] app_type[69937]
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> send_audvoltable
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_GAIN_DEP_STEP_TABLE_SIZE
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_GAIN_DEP_STEP_TABLE, vol index 0
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> AUDIO_SET_VOL_CAL cal type = 12
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_STREAM_TABLE_SIZE
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> send_audstrmtable
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_STREAM_TABLE_V2
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> audstrm_cal->cal_type.cal_data.cal_size = 16
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> send_afe_topology
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AFE_TOPOLOGY_ID
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> GET_AFE_TOPOLOGY_ID for adcd_id 15, Topology Id 1025e
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> send_afe_cal
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AFE_COMMON_TABLE_SIZE
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AFE_COMMON_TABLE
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> AUDIO_SET_AFE_CAL cal_type[16] acdb_id[15]
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> send_hw_delay : acdb_id = 15 path = 0
08-22 16:38:32.641   746  3209 D ACDB-LOADER: ACDB -> ACDB_AVSYNC_INFO: ACDB_CMD_GET_DEVICE_PROPERTY
08-22 16:38:32.641   746  3209 D audio_hw_primary: enable_audio_route: apply mixer and update path: low-latency-playback
08-22 16:38:32.641   746  3209 D audio_route: Apply path: low-latency-playback
08-22 16:38:32.643   746  3209 D audio_hw_primary: select_devices: done
08-22 16:38:32.643   746  3209 D msm8974_platform: platform_set_channel_map mixer_ctl_name:Playback Channel Map13
08-22 16:38:32.643   746  3209 D msm8974_platform: platform_set_channel_map: set mapping(1 2 0 0 0 0 0 0) for channel:2
08-22 16:38:32.644   746  3209 E msm8974_platform: platform_set_channel_map: Could not set ctl, error:-1 ch_count:2
08-22 16:38:32.699 15954 15993 D BluetoothGatt: connect() - device: F0:9E:9E:0F:E9:25, auto: false, eattSupport: false
08-22 16:38:32.700 15954 15993 D BluetoothGatt: registerApp()
08-22 16:38:32.700 15954 15993 D BluetoothGatt: registerApp() - UUID=3ed78fd7-3838-4c03-8e55-fa07b307888b
08-22 16:38:32.701  1593  2663 D BtGatt.GattService: registerClient() - UUID=3ed78fd7-3838-4c03-8e55-fa07b307888b
08-22 16:38:32.702  1593  2395 V bt_stack: [VERBOSE1:bta_gattc_act.cc(210)] bta_gattc_register: state:2
08-22 16:38:32.702  1593  2395 I bt_stack: [INFO:gatt_api.cc(1228)] GATT_Register f552927f-42f5-b3a5-8e08-a17ed00d197f
08-22 16:38:32.702  1593  2395 I bt_stack: [INFO:gatt_api.cc(1249)] allocated gatt_if=5
08-22 16:38:32.702  1593  2395 V bt_stack: [VERBOSE1:gatt_api.cc(1374)] GATT_StartIf gatt_if=5
08-22 16:38:32.702  1593  2395 V bt_stack: [VERBOSE1:gatt_utils.cc(293)] gatt_find_the_connected_bda start_idx=0
08-22 16:38:32.702  1593  2395 V bt_stack: [VERBOSE1:gatt_utils.cc(305)]  found=0 found_idx=10
08-22 16:38:32.702  1593  1743 D BtGatt.GattService: onClientRegistered() - UUID=3ed78fd7-3838-4c03-8e55-fa07b307888b, clientIf=5
08-22 16:38:32.703 15954 16057 D BluetoothGatt: onClientRegistered() - status=0 clientIf=5
08-22 16:38:32.705  1593  2663 D BtGatt.GattService: clientConnect() - address=F0:9E:9E:0F:E9:25, isDirect=true, opportunistic=false, phy=1
08-22 16:38:32.705  1593  1743 D bt_btif_config: btif_get_address_type: Device [f0:9e:9e:0f:e9:25] address type 0
08-22 16:38:32.705  1593  1743 D bt_btif_config: btif_get_device_type: Device [f0:9e:9e:0f:e9:25] type 3
08-22 16:38:32.705  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr f0:9e:9e:0f:e9:25
08-22 16:38:32.705  1593  2395 V bt_stack: [VERBOSE1:bta_gattc_main.cc(361)] bta_gattc_hdl_event: Event:BTA_GATTC_API_OPEN_EVT
08-22 16:38:32.705  1593  2395 V bt_stack: [VERBOSE1:bta_gattc_main.cc(305)] bta_gattc_sm_execute: State 0x00 [GATTC_IDLE_ST], Event 0x1f00[BTA_GATTC_API_OPEN_EVT]
08-22 16:38:32.705  1593  2395 I bt_stack: [INFO:gatt_api.cc(1418)] GATT_Connect gatt_if=5, address=f0:9e:9e:0f:e9:25 is_direct 1
08-22 16:38:32.705  1593  2395 V bt_stack: [VERBOSE1:btm_ble_bgconn.cc(561)] BTM_GetLeDisconnectStatus is_le_disc_pending 0
08-22 16:38:32.705  1593  2395 V bt_stack: [VERBOSE1:gatt_main.cc(283)] gatt_connect
08-22 16:38:32.705  1593  2395 V bt_stack: [VERBOSE1:gatt_main.cc(1823)] gatt_get_ch_state: ch_state=0
08-22 16:38:32.705  1593  2395 V bt_stack: [VERBOSE1:gatt_main.cc(1814)] gatt_set_ch_state: old=0 new=0x02
08-22 16:38:32.705  1593  2395 V bt_stack: [VERBOSE1:gatt_main.cc(1823)] gatt_get_ch_state: ch_state=2
08-22 16:38:32.705  1593  2395 V bt_stack: [VERBOSE1:connection_manager.cc(219)] direct_connect_add : address:f0:9e:9e:0f:e9:25
08-22 16:38:32.705  1593  2395 V bt_stack: [VERBOSE1:btm_ble_bgconn.cc(514)] BTM_WhiteListAdd: f0:9e:9e:0f:e9:25
08-22 16:38:32.705  1593  2395 V bt_stack: [VERBOSE1:connection_manager.cc(53)] scheduling timer %spc:0x788f14eaec
08-22 16:38:32.705  1593  2395 V bt_stack: [VERBOSE1:gatt_main.cc(470)] gatt_update_app_use_link_flag: is_add=1 chk_link=0
08-22 16:38:32.705  1593  2395 V bt_stack: [VERBOSE1:gatt_main.cc(436)] gatt_update_app_hold_link_status
08-22 16:38:32.705  1593  2395 V bt_stack: [VERBOSE1:gatt_main.cc(440)] added gatt_if=5
08-22 16:38:32.705   749   749 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK ON
08-22 16:38:32.705  1593  2395 V bt_stack: [VERBOSE1:gatt_main.cc(1823)] gatt_get_ch_state: ch_state=2
08-22 16:38:32.706  1593  2395 V bt_stack: [VERBOSE1:gatt_api.cc(1627)] GATT_GetConnIdIfConnected status= 0
08-22 16:38:32.706  1593  2395 V bt_stack: [VERBOSE1:bta_gattc_main.cc(341)] bta_gattc_sm_execute: GATTC State Change: 0x00 -> 0x01 after Event 0x1f00
08-22 16:38:32.708   749   749 I vendor.qti.bluetooth@1.0-ibs_handler: DeviceWakeUp: Writing IBS_WAKE_IND
08-22 16:38:32.708   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_ACK: 0xFC
08-22 16:38:32.708   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Signal wack_cond_
08-22 16:38:32.708   749   749 D vendor.qti.bluetooth@1.0-ibs_handler: DeviceWakeUp: Unblocked from waiting for FC, pthread_cond_timedwait ret = 0
08-22 16:38:32.709   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_IND: 0xFD
08-22 16:38:32.709   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Writing IBS_WAKE_ACK
08-22 16:38:32.718   746  3209 D audio_hw_primary: start_output_stream: exit
08-22 16:38:32.751   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_SLEEP_IND: 0xFE
08-22 16:38:33.389 15954 15993 D valid   : parameter Ecg
08-22 16:38:33.389 15954 15993 D valid   : parameter BpSys
08-22 16:38:33.389 15954 15993 D valid   : parameter Fall
08-22 16:38:33.389 15954 15993 D valid   : parameter BpDia
08-22 16:38:33.389 15954 15993 D valid   : parameter Spo2
08-22 16:38:33.389 15954 15993 D valid   : parameter Resp
08-22 16:38:33.390 15954 15993 D valid   : parameter PR
08-22 16:38:33.390 15954 15993 D valid   : parameter Temp
08-22 16:38:33.390 15954 15993 D valid   : parameter ConnectivityAlarm
08-22 16:38:33.616 15954 15954 D DatabaseDebug: Passphrase: mykey
08-22 16:38:33.710   749  2398 I vendor.qti.bluetooth@1.0-ibs_handler: DeviceSleep: TX Awake, Sending SLEEP_IND
08-22 16:38:33.710   749  2398 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK OFF
08-22 16:38:33.715 15954 16057 I OpenGLRenderer: Davey! duration=9223349047657ms; Flags=0, FrameTimelineVsyncId=576939, IntendedVsync=22989196905423, Vsync=22989280238753, InputEventId=0, HandleInputStart=22989296194093, AnimationStart=22989296201645, PerformTraversalsStart=22989296206541, DrawStart=22989297622791, FrameDeadline=22989213572089, FrameInterval=22989296172322, FrameStartTime=16666666, SyncQueued=22989299666384, SyncStart=22989300199978, IssueDrawCommandsStart=22989300440238, SwapBuffers=22989303926072, FrameCompleted=9223372036854775807, DequeueBufferDuration=25885, QueueBufferDuration=748541, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22989305133988, DisplayPresentTime=0, 
08-22 16:38:33.752 15954 15971 I OpenGLRenderer: Davey! duration=9223349047525ms; Flags=0, FrameTimelineVsyncId=576942, IntendedVsync=22989313542053, Vsync=22989313542053, InputEventId=0, HandleInputStart=22989317844718, AnimationStart=22989317851853, PerformTraversalsStart=22989317856749, DrawStart=22989323319926, FrameDeadline=22989346875385, FrameInterval=22989317818832, FrameStartTime=16666666, SyncQueued=22989323614874, SyncStart=22989339068572, IssueDrawCommandsStart=22989339222061, SwapBuffers=22989341246645, FrameCompleted=9223372036854775807, DequeueBufferDuration=20416, QueueBufferDuration=1565156, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22989344028416, DisplayPresentTime=0, 
08-22 16:38:33.860   749  2099 D vendor.qti.bluetooth@1.0-wake_lock: Release wakelock is released 
08-22 16:38:33.864 15954 15971 I OpenGLRenderer: Davey! duration=9223349047407ms; Flags=0, FrameTimelineVsyncId=576945, IntendedVsync=22989446754928, Vsync=22989446754928, InputEventId=0, HandleInputStart=22989448928988, AnimationStart=22989448943103, PerformTraversalsStart=22989448949249, DrawStart=22989449404718, FrameDeadline=22989480088260, FrameInterval=22989448897582, FrameStartTime=16666666, SyncQueued=22989449902947, SyncStart=22989450242947, IssueDrawCommandsStart=22989450485186, SwapBuffers=22989453312843, FrameCompleted=9223372036854775807, DequeueBufferDuration=50573, QueueBufferDuration=1575938, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22989455715291, DisplayPresentTime=0, 
08-22 16:38:33.979 15954 15971 I OpenGLRenderer: Davey! duration=9223349047291ms; Flags=0, FrameTimelineVsyncId=576948, IntendedVsync=22989563357400, Vsync=22989563357400, InputEventId=0, HandleInputStart=22989564954093, AnimationStart=22989564971280, PerformTraversalsStart=22989564977634, DrawStart=22989565626697, FrameDeadline=22989596690732, FrameInterval=22989564899197, FrameStartTime=16666666, SyncQueued=22989566203155, SyncStart=22989566587009, IssueDrawCommandsStart=22989566802322, SwapBuffers=22989569892790, FrameCompleted=9223372036854775807, DequeueBufferDuration=49636, QueueBufferDuration=959531, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22989571669301, DisplayPresentTime=0, 
08-22 16:38:34.392 15954 15993 D valid   : parameter Ecg
08-22 16:38:34.392 15954 15993 D valid   : parameter BpSys
08-22 16:38:34.392 15954 15993 D valid   : parameter Fall
08-22 16:38:34.392 15954 15993 D valid   : parameter BpDia
08-22 16:38:34.392 15954 15993 D valid   : parameter Spo2
08-22 16:38:34.392 15954 15993 D valid   : parameter Resp
08-22 16:38:34.392 15954 15993 D valid   : parameter PR
08-22 16:38:34.392 15954 15993 D valid   : parameter Temp
08-22 16:38:34.392 15954 15993 D valid   : parameter ConnectivityAlarm
08-22 16:38:34.595 15954 15971 I OpenGLRenderer: Davey! duration=9223349046674ms; Flags=0, FrameTimelineVsyncId=576967, IntendedVsync=22990179868014, Vsync=22990179868014, InputEventId=0, HandleInputStart=22990187494509, AnimationStart=22990187507842, PerformTraversalsStart=22990187513728, DrawStart=22990187956853, FrameDeadline=22990213201346, FrameInterval=22990187467426, FrameStartTime=16666666, SyncQueued=22990188357790, SyncStart=22990188813155, IssueDrawCommandsStart=22990189021905, SwapBuffers=22990191947374, FrameCompleted=9223372036854775807, DequeueBufferDuration=46719, QueueBufferDuration=1207396, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22990193756644, DisplayPresentTime=0, 
08-22 16:38:34.716 15954 15954 D DatabaseDebug: Passphrase: mykey
08-22 16:38:34.794 15954 15971 I OpenGLRenderer: Davey! duration=9223349046558ms; Flags=0, FrameTimelineVsyncId=576970, IntendedVsync=22990296592035, Vsync=22990379925365, InputEventId=0, HandleInputStart=22990392682165, AnimationStart=22990392688988, PerformTraversalsStart=22990392693728, DrawStart=22990392966176, FrameDeadline=22990329925367, FrameInterval=22990392667998, FrameStartTime=16666666, SyncQueued=22990393365707, SyncStart=22990393531540, IssueDrawCommandsStart=22990393634873, SwapBuffers=22990395215603, FrameCompleted=9223372036854775807, DequeueBufferDuration=22552, QueueBufferDuration=613854, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22990396373363, DisplayPresentTime=0, 
08-22 16:38:34.828 15954 15971 I OpenGLRenderer: Davey! duration=9223349046440ms; Flags=0, FrameTimelineVsyncId=576974, IntendedVsync=22990413366253, Vsync=22990413366253, InputEventId=0, HandleInputStart=22990417321123, AnimationStart=22990417332946, PerformTraversalsStart=22990417337842, DrawStart=22990419877113, FrameDeadline=22990430032919, FrameInterval=22990417305342, FrameStartTime=16666666, SyncQueued=22990421160082, SyncStart=22990421753571, IssueDrawCommandsStart=22990422104509, SwapBuffers=22990425242634, FrameCompleted=9223372036854775807, DequeueBufferDuration=23229, QueueBufferDuration=1490625, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22990427594665, DisplayPresentTime=0, 
08-22 16:38:34.963 15954 15971 I OpenGLRenderer: Davey! duration=9223349046307ms; Flags=0, FrameTimelineVsyncId=576979, IntendedVsync=22990546638436, Vsync=22990546638436, InputEventId=0, HandleInputStart=22990548304196, AnimationStart=22990548316644, PerformTraversalsStart=22990548322894, DrawStart=22990548774821, FrameDeadline=22990579971768, FrameInterval=22990548268884, FrameStartTime=16666666, SyncQueued=22990549269821, SyncStart=22990549618936, IssueDrawCommandsStart=22990550101748, SwapBuffers=22990553738988, FrameCompleted=9223372036854775807, DequeueBufferDuration=85365, QueueBufferDuration=1232448, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22990555764353, DisplayPresentTime=0, 
08-22 16:38:35.230  1359 14821 W InputManager-JNI: Input channel object 'e49fda7 Toast (client)' was disposed without first being removed with the input manager!
08-22 16:38:35.237  1359 14821 W NotificationService: Toast already killed. pkg=com.bodymount.app token=android.os.BinderProxy@fbcd516
08-22 16:38:35.328 15954 16057 I OpenGLRenderer: Davey! duration=9223349045941ms; Flags=0, FrameTimelineVsyncId=577024, IntendedVsync=22990913232693, Vsync=22990913232693, InputEventId=0, HandleInputStart=22990914041175, AnimationStart=22990914053311, PerformTraversalsStart=22990914059509, DrawStart=22990914482738, FrameDeadline=22990946566025, FrameInterval=22990914010238, FrameStartTime=16666666, SyncQueued=22990914888259, SyncStart=22990915016280, IssueDrawCommandsStart=22990915219040, SwapBuffers=22990917273571, FrameCompleted=9223372036854775807, DequeueBufferDuration=45625, QueueBufferDuration=949062, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22990918901957, DisplayPresentTime=0, 
08-22 16:38:35.395 15954 15993 D valid   : parameter Ecg
08-22 16:38:35.395 15954 15993 D valid   : parameter BpSys
08-22 16:38:35.395 15954 15993 D valid   : parameter Fall
08-22 16:38:35.395 15954 15993 D valid   : parameter BpDia
08-22 16:38:35.395 15954 15993 D valid   : parameter Spo2
08-22 16:38:35.395 15954 15993 D valid   : parameter Resp
08-22 16:38:35.395 15954 15993 D valid   : parameter PR
08-22 16:38:35.395 15954 15993 D valid   : parameter Temp
08-22 16:38:35.395 15954 15993 D valid   : parameter ConnectivityAlarm
08-22 16:38:35.820 15954 15954 D DatabaseDebug: Passphrase: mykey
08-22 16:38:35.881   746   746 D audio_hw_primary: out_standby: enter: stream (0xf2944330) usecase(1: low-latency-playback)
08-22 16:38:35.911 15954 16057 I OpenGLRenderer: Davey! duration=9223349045441ms; Flags=0, FrameTimelineVsyncId=577036, IntendedVsync=22991413436013, Vsync=22991496769343, InputEventId=0, HandleInputStart=22991505843154, AnimationStart=22991505851071, PerformTraversalsStart=22991505856175, DrawStart=22991506851019, FrameDeadline=22991446769345, FrameInterval=22991505826436, FrameStartTime=16666666, SyncQueued=22991508736644, SyncStart=22991508987633, IssueDrawCommandsStart=22991509182113, SwapBuffers=22991511369404, FrameCompleted=9223372036854775807, DequeueBufferDuration=24010, QueueBufferDuration=763802, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22991512845654, DisplayPresentTime=0, 
08-22 16:38:35.941   746   746 D audio_hw_primary: disable_audio_route: reset and update mixer path: low-latency-playback
08-22 16:38:35.943   746   746 D soundtrigger: audio_extn_sound_trigger_update_stream_status: uc_info->id 1 of type 0 for Event 2, with Raise=0
08-22 16:38:35.943   746   746 D hardware_info: hw_info_append_hw_type : device_name = speaker
08-22 16:38:35.943   746   746 D audio_hw_primary: disable_snd_device: snd_device(2: speaker)
08-22 16:38:35.953   746   746 I soundtrigger: audio_extn_sound_trigger_update_device_status: device 0x2 of type 0 for Event 0, with Raise=0
08-22 16:38:36.278 15954 16057 I OpenGLRenderer: Davey! duration=9223349044990ms; Flags=0, FrameTimelineVsyncId=577049, IntendedVsync=22991863615614, Vsync=22991863615614, InputEventId=0, HandleInputStart=22991865030758, AnimationStart=22991865042477, PerformTraversalsStart=22991865048519, DrawStart=22991865458310, FrameDeadline=22991880282280, FrameInterval=22991865001592, FrameStartTime=16666666, SyncQueued=22991865857998, SyncStart=22991866193623, IssueDrawCommandsStart=22991866398727, SwapBuffers=22991869271279, FrameCompleted=9223372036854775807, DequeueBufferDuration=47187, QueueBufferDuration=1329687, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22991871788258, DisplayPresentTime=0, 
08-22 16:38:36.398 15954 15993 D valid   : parameter Ecg
08-22 16:38:36.398 15954 15993 D valid   : parameter BpSys
08-22 16:38:36.398 15954 15993 D valid   : parameter Fall
08-22 16:38:36.398 15954 15993 D valid   : parameter BpDia
08-22 16:38:36.398 15954 15993 D valid   : parameter Spo2
08-22 16:38:36.399 15954 15993 D valid   : parameter Resp
08-22 16:38:36.399 15954 15993 D valid   : parameter PR
08-22 16:38:36.399 15954 15993 D valid   : parameter Temp
08-22 16:38:36.399 15954 15993 D valid   : parameter ConnectivityAlarm
08-22 16:38:36.722   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_IND: 0xFD
08-22 16:38:36.722   749  2393 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK ON
08-22 16:38:36.726   749  2393 D vendor.qti.bluetooth@1.0-wake_lock: Acquire wakelock is acquired 
08-22 16:38:36.726   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Writing IBS_WAKE_ACK
08-22 16:38:36.729  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 30:e7:bc:de:fa:ae
08-22 16:38:36.729  1593  2395 E bt_btm  : Ignoring RNR as the state is not BTM_SEC_STATE_GETTING_NAME
08-22 16:38:36.729  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 4c:49:6c:54:ff:0f
08-22 16:38:36.729  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 5d:e1:1e:ff:f5:ab
08-22 16:38:36.729  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr e3:31:77:8d:54:26
08-22 16:38:36.729  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 48:74:12:36:51:b6
08-22 16:38:36.729  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 4f:55:0d:58:a4:e1
08-22 16:38:36.729  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(1034)] BTM_InqDbRead: bd addr 94:32:51:02:2c:19
08-22 16:38:36.729  1593  2395 V bt_stack: [VERBOSE1:btm_inq.cc(960)] BTM_ReadRemoteDeviceName: bd addr 94:32:51:02:2c:19
08-22 16:38:36.729  1593  2395 V bt_stack: [VERBOSE1:btm_acl.cc(3014)] connecting_bda: 30:e7:bc:de:fa:ae
08-22 16:38:36.729   749   749 I vendor.qti.bluetooth@1.0-ibs_handler: DeviceWakeUp: Writing IBS_WAKE_IND
08-22 16:38:36.731   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_WAKE_ACK: 0xFC
08-22 16:38:36.731   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Signal wack_cond_
08-22 16:38:36.731   749   749 D vendor.qti.bluetooth@1.0-ibs_handler: DeviceWakeUp: Unblocked from waiting for FC, pthread_cond_timedwait ret = 0
08-22 16:38:36.772   749  2393 I vendor.qti.bluetooth@1.0-ibs_handler: ProcessIbsCmd: Received IBS_SLEEP_IND: 0xFE
08-22 16:38:36.914 15954 15954 D DatabaseDebug: Passphrase: mykey
08-22 16:38:36.995 15954 15971 I OpenGLRenderer: Davey! duration=9223349044340ms; Flags=0, FrameTimelineVsyncId=577072, IntendedVsync=22992513641004, Vsync=22992580307668, InputEventId=0, HandleInputStart=22992581212008, AnimationStart=22992581219716, PerformTraversalsStart=22992581224612, DrawStart=22992582666123, FrameDeadline=22992546974336, FrameInterval=22992581193779, FrameStartTime=16666666, SyncQueued=22992585144612, SyncStart=22992585838675, IssueDrawCommandsStart=22992586028258, SwapBuffers=22992588193675, FrameCompleted=9223372036854775807, DequeueBufferDuration=24375, QueueBufferDuration=708906, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22992589508102, DisplayPresentTime=0, 
08-22 16:38:37.146 15954 15971 I OpenGLRenderer: Davey! duration=9223349044124ms; Flags=0, FrameTimelineVsyncId=577080, IntendedVsync=22992730332371, Vsync=22992730332371, InputEventId=0, HandleInputStart=22992731853206, AnimationStart=22992731864300, PerformTraversalsStart=22992731870498, DrawStart=22992732325289, FrameDeadline=22992763665703, FrameInterval=22992731822529, FrameStartTime=16666666, SyncQueued=22992732772060, SyncStart=22992733091643, IssueDrawCommandsStart=22992733328362, SwapBuffers=22992736058727, FrameCompleted=9223372036854775807, DequeueBufferDuration=55416, QueueBufferDuration=1374635, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22992738173831, DisplayPresentTime=0, 
08-22 16:38:37.280 15954 15971 I OpenGLRenderer: Davey! duration=9223349043990ms; Flags=0, FrameTimelineVsyncId=577083, IntendedVsync=22992863804967, Vsync=22992863804967, InputEventId=0, HandleInputStart=22992865472685, AnimationStart=22992865491018, PerformTraversalsStart=22992865499664, DrawStart=22992865933050, FrameDeadline=22992897138299, FrameInterval=22992865404404, FrameStartTime=16666666, SyncQueued=22992866372216, SyncStart=22992866692216, IssueDrawCommandsStart=22992866899300, SwapBuffers=22992869793050, FrameCompleted=9223372036854775807, DequeueBufferDuration=49323, QueueBufferDuration=1085520, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22992871772789, DisplayPresentTime=0, 
08-22 16:38:37.402 15954 15993 D valid   : parameter Ecg
08-22 16:38:37.402 15954 15993 D valid   : parameter BpSys
08-22 16:38:37.402 15954 15993 D valid   : parameter Fall
08-22 16:38:37.402 15954 15993 D valid   : parameter BpDia
08-22 16:38:37.402 15954 15993 D valid   : parameter Spo2
08-22 16:38:37.402 15954 15993 D valid   : parameter Resp
08-22 16:38:37.402 15954 15993 D valid   : parameter PR
08-22 16:38:37.402 15954 15993 D valid   : parameter Temp
08-22 16:38:37.402 15954 15993 D valid   : parameter ConnectivityAlarm
08-22 16:38:37.731   749  2398 I vendor.qti.bluetooth@1.0-ibs_handler: DeviceSleep: TX Awake, Sending SLEEP_IND
08-22 16:38:37.731   749  2398 D vendor.qti.bluetooth@1.0-ibs_handler: SerialClockVote: vote for UART CLK OFF
08-22 16:38:37.882   749  2099 D vendor.qti.bluetooth@1.0-wake_lock: Release wakelock is released 
08-22 16:38:38.001 15954 15954 D DatabaseDebug: Passphrase: mykey
08-22 16:38:38.404 15954 15993 D valid   : parameter Ecg
08-22 16:38:38.405 15954 15993 D valid   : parameter BpSys
08-22 16:38:38.405 15954 15993 D valid   : parameter Fall
08-22 16:38:38.405 15954 15993 D valid   : parameter BpDia
08-22 16:38:38.405 15954 15993 D valid   : parameter Spo2
08-22 16:38:38.405 15954 15993 D valid   : parameter Resp
08-22 16:38:38.405 15954 15993 D valid   : parameter PR
08-22 16:38:38.405 15954 15993 D valid   : parameter Temp
08-22 16:38:38.405 15954 15993 D valid   : parameter ConnectivityAlarm
08-22 16:38:38.980 15954 16057 I OpenGLRenderer: Davey! duration=9223349042291ms; Flags=0, FrameTimelineVsyncId=577149, IntendedVsync=22994563381530, Vsync=22994563381530, InputEventId=0, HandleInputStart=22994570138882, AnimationStart=22994570152945, PerformTraversalsStart=22994570158830, DrawStart=22994570587424, FrameDeadline=22994580048196, FrameInterval=22994570121643, FrameStartTime=16666666, SyncQueued=22994570967320, SyncStart=22994571272007, IssueDrawCommandsStart=22994571421851, SwapBuffers=22994572812684, FrameCompleted=9223372036854775807, DequeueBufferDuration=37136, QueueBufferDuration=807917, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22994574143570, DisplayPresentTime=0, 
08-22 16:38:39.099 15954 15954 D DatabaseDebug: Passphrase: mykey
08-22 16:38:39.178 15954 15971 I OpenGLRenderer: Davey! duration=9223349042174ms; Flags=0, FrameTimelineVsyncId=577152, IntendedVsync=22994680019271, Vsync=22994763352601, InputEventId=0, HandleInputStart=22994767865705, AnimationStart=22994767873830, PerformTraversalsStart=22994767878726, DrawStart=22994770166174, FrameDeadline=22994696685937, FrameInterval=22994767855497, FrameStartTime=16666666, SyncQueued=22994772426903, SyncStart=22994772828518, IssueDrawCommandsStart=22994773268778, SwapBuffers=22994776090445, FrameCompleted=9223372036854775807, DequeueBufferDuration=26302, QueueBufferDuration=1386615, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22994778092268, DisplayPresentTime=0, 
08-22 16:38:39.193 15954 15971 I OpenGLRenderer: Davey! duration=9223349042074ms; Flags=0, FrameTimelineVsyncId=577155, IntendedVsync=22994780007189, Vsync=22994780007189, InputEventId=0, HandleInputStart=22994789765913, AnimationStart=22994789775497, PerformTraversalsStart=22994789780549, DrawStart=22994791257997, FrameDeadline=22994813340521, FrameInterval=22994789753934, FrameStartTime=16666666, SyncQueued=22994791858205, SyncStart=22994792067424, IssueDrawCommandsStart=22994792218309, SwapBuffers=22994795016799, FrameCompleted=9223372036854775807, DequeueBufferDuration=23386, QueueBufferDuration=840052, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22994796348153, DisplayPresentTime=0, 
08-22 16:38:39.407 15954 15993 D valid   : parameter Ecg
08-22 16:38:39.407 15954 15993 D valid   : parameter BpSys
08-22 16:38:39.407 15954 15993 D valid   : parameter Fall
08-22 16:38:39.407 15954 15993 D valid   : parameter BpDia
08-22 16:38:39.407 15954 15993 D valid   : parameter Spo2
08-22 16:38:39.407 15954 15993 D valid   : parameter Resp
08-22 16:38:39.407 15954 15993 D valid   : parameter PR
08-22 16:38:39.407 15954 15993 D valid   : parameter Temp
08-22 16:38:39.407 15954 15993 D valid   : parameter ConnectivityAlarm
08-22 16:38:39.462 15954 15971 I OpenGLRenderer: Davey! duration=9223349041807ms; Flags=0, FrameTimelineVsyncId=577164, IntendedVsync=22995046557470, Vsync=22995046557470, InputEventId=0, HandleInputStart=22995047771695, AnimationStart=22995047785080, PerformTraversalsStart=22995047792111, DrawStart=22995048221747, FrameDeadline=22995063224136, FrameInterval=22995047749715, FrameStartTime=16666666, SyncQueued=22995048650288, SyncStart=22995048966695, IssueDrawCommandsStart=22995049200601, SwapBuffers=22995051636695, FrameCompleted=9223372036854775807, DequeueBufferDuration=56354, QueueBufferDuration=1552604, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22995053662111, DisplayPresentTime=0, 
08-22 16:38:40.188 15954 15954 D DatabaseDebug: Passphrase: mykey
08-22 16:38:40.409 15954 15993 D valid   : parameter Ecg
08-22 16:38:40.409 15954 15993 D valid   : parameter BpSys
08-22 16:38:40.409 15954 15993 D valid   : parameter Fall
08-22 16:38:40.409 15954 15993 D valid   : parameter BpDia
08-22 16:38:40.409 15954 15993 D valid   : parameter Spo2
08-22 16:38:40.409 15954 15993 D valid   : parameter Resp
08-22 16:38:40.409 15954 15993 D valid   : parameter PR
08-22 16:38:40.410 15954 15993 D valid   : parameter Temp
08-22 16:38:40.410 15954 15993 D valid   : parameter ConnectivityAlarm
08-22 16:38:40.528 15954 15971 I OpenGLRenderer: Davey! duration=9223349040741ms; Flags=0, FrameTimelineVsyncId=577191, IntendedVsync=22996113390173, Vsync=22996113390173, InputEventId=0, HandleInputStart=22996114916069, AnimationStart=22996114929142, PerformTraversalsStart=22996114935496, DrawStart=22996115421173, FrameDeadline=22996146723505, FrameInterval=22996114896173, FrameStartTime=16666666, SyncQueued=22996115844663, SyncStart=22996116045757, IssueDrawCommandsStart=22996116305965, SwapBuffers=22996119255861, FrameCompleted=9223372036854775807, DequeueBufferDuration=55000, QueueBufferDuration=1074270, GpuCompleted=9223372036854775807, SwapBuffersCompleted=22996121117059, DisplayPresentTime=0, 
